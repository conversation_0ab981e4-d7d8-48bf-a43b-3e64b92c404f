#!/usr/bin/env python3
"""
Webook Login API Service

This Flask API service logs into Webook accounts, extracts access tokens, and stores them in MySQL.
It accepts username, password, and optional proxy for each login attempt.

Setup:
1. Install requirements: 
   pip install flask flask-sqlalchemy pymysql cryptography httpx

2. Configure MySQL connection in config.py or environment variables.

3. Initialize database:
   flask init-db

4. Run the API:
   flask run --host=0.0.0.0 --port=5000

API Endpoints:
- POST /api/login - Login with Webook credentials
- GET /api/token/<email> - Get stored token for an email
"""

import os
import json
import time
import random
import hashlib
import asyncio
from datetime import datetime
from typing import Dict, Optional, Tuple, Any

from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
import httpx

# Import functions from bypass_captcha.py
from bypass_captcha import recaptcha1

# Initialize Flask app
app = Flask(__name__)

# Configuration
class Config:
    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI', 
        'mysql+pymysql://webook_user:webook_password@localhost/webook_db'
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_change_in_production')
    
    # API settings
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

app.config.from_object(Config)

# Initialize database
db = SQLAlchemy(app)

# Database models
class Account(db.Model):
    __tablename__ = 'accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    access_token = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Account {self.email}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'access_token': self.access_token,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# Database initialization command
@app.cli.command('init-db')
def init_db_command():
    """Initialize the database."""
    db.create_all()
    print('Initialized the database.')

# Helper functions
async def random_user_agent():
    """Generate a user agent."""
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0"

def generate_signature(email):
    """Generate a signature for the login request."""
    random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=20))
    md5_hash = hashlib.md5(f"{email}e0q76ti7ikioqnqb{random_str}".encode()).hexdigest()
    return f"{md5_hash}|{random_str}"

async def login_to_webook(email: str, password: str, proxy: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
    """
    Log into Webook and extract the access token.
    
    Args:
        email: User's email address
        password: User's password
        proxy: Optional proxy in format ip:port or user:pass@ip:port
        
    Returns:
        Tuple of (success, response_data)
        If successful, response_data contains the access token
        If failed, response_data contains error information
    """
    # Setup proxy if provided
    if proxy:
        proxy_url = None
        if '//' in proxy:
            proxy_url = proxy
        else:
            if '@' in proxy:  # With authentication
                proxy_url = f"socks5://{proxy}"
            else:  # Without authentication
                proxy_url = f"socks5://{proxy}"
    
    # Generate user agent
    ua = await random_user_agent()
    
    try:
        # Solve captcha
        print(f"Solving captcha for {email}...")
        captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcvYHooAAAAAC-G46bpymJKtIwfDQpg9DsHPMpL&co=aHR0cHM6Ly93ZWJvb2suY29tOjQ0Mw..&hl=en&v=IyZ984yGrXrBd6ihLOYGwy9X&size=invisible&cb=b7e5fsomtcd"
        cap = await recaptcha1(captcha_url, False, ua)
        if not cap:
            return False, {"error": "Failed to solve captcha"}
        
        # Generate signature
        signature = generate_signature(email)
        
        # Create HTTP client with proxy if available
        client_kwargs = {"follow_redirects": True}
        if proxy_url:
            client_kwargs["proxy"] = proxy_url
    
        async with httpx.AsyncClient(**client_kwargs) as client:
            # Prepare login payload
            payload = {
                "email": email,
                "password": password,
                "captcha": cap,
                "signature": signature,
                "app_source": "rs",
                "login_with": "email",
                "lang": "en"
            }
            
            # Prepare headers
            headers = {
                "accept": "application/json",
                "accept-language": "en-US,en;q=0.9",
                "authorization": "Bearer",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "dnt": "1",
                "origin": "https://webook.com",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "token": "e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2",
                "user-agent": ua
            }
            
            # Send login request
            print(f"Sending login request for {email}...")
            response = await client.post(
                "https://api.webook.com/api/v2/login",
                headers=headers,
                json=payload,
            )
            
            # Process response
            try:
                response_data = response.json()
                
                # Check if login was successful and access token exists
                if response_data.get('data', {}).get('access_token'):
                    access_token = response_data['data']['access_token']
                    print(f"Login successful for {email}! Access token retrieved.")
                    return True, {"access_token": access_token}
                else:
                    error_msg = response_data.get('message', 'Unknown error')
                    print(f"Login failed for {email}. Error: {error_msg}")
                    return False, {"error": error_msg, "response": response_data}
                
            except json.JSONDecodeError:
                print(f"Failed to parse JSON response for {email}.")
                return False, {"error": "Invalid JSON response", "raw_response": response.text[:500]}
    
    except Exception as e:
        print(f"Exception during login for {email}: {str(e)}")
        return False, {"error": str(e)}

# API Routes
@app.route('/api/login', methods=['POST'])
def api_login():
    """
    API endpoint to login to Webook and get an access token.
    
    Required POST parameters:
    - email: User's email
    - password: User's password
    
    Optional parameters:
    - proxy: Proxy in format ip:port or user:pass@ip:port
    
    Returns:
    - JSON with access_token on success
    - JSON with error message on failure
    """
    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    email = data.get('email')
    password = data.get('password')
    proxy = data.get('proxy')
    
    if not email or not password:
        return jsonify({"error": "Email and password are required"}), 400
    
    # Run the async login function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    success, result = loop.run_until_complete(login_to_webook(email, password, proxy))
    loop.close()
    
    if success:
        # Store the token in the database
        existing_account = Account.query.filter_by(email=email).first()
        
        if existing_account:
            # Update existing account
            existing_account.password = password
            existing_account.access_token = result["access_token"]
            existing_account.updated_at = datetime.utcnow()
        else:
            # Create new account
            new_account = Account(
                email=email,
                password=password,
                access_token=result["access_token"]
            )
            db.session.add(new_account)
        
        try:
            db.session.commit()
            print(f"Saved token to database for {email}")
        except Exception as e:
            db.session.rollback()
            print(f"Database error: {str(e)}")
            return jsonify({"error": f"Database error: {str(e)}"}), 500
        
        # Return the access token
        return jsonify({
            "status": "success",
            "email": email,
            "access_token": result["access_token"]
        })
    else:
        # Return error
        return jsonify({
            "status": "error",
            "email": email,
            "error": result.get("error", "Unknown error"),
            "details": result
        }), 401

@app.route('/api/token/<email>', methods=['GET'])
def get_token(email):
    """
    API endpoint to get a stored token for an email.
    
    Returns:
    - JSON with account details including access_token
    - 404 if account not found
    """
    account = Account.query.filter_by(email=email).first()
    
    if not account:
        return jsonify({"error": "Account not found"}), 404
    
    return jsonify({
        "status": "success",
        "account": account.to_dict()
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found"}), 404

@app.errorhandler(500)
def server_error(error):
    return jsonify({"error": "Server error"}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)