#!/usr/bin/env python3
"""
Enhanced Email Verification API Endpoint

This module adds enhanced email verification functionality to the Webook Admin API.
It provides endpoints to trigger the verification process for accounts using a pool
of verification emails.
"""

import os
import asyncio
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from flask_login import login_required, current_user
from dotenv import load_dotenv
from models import Account, VerificationEmail, db

# Import the verification function from the verify_email.py script
from verify_email import verify_webook_account

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='email_verification.log'
)
logger = logging.getLogger('email_verifier')

# Create blueprint
email_verifier_bp = Blueprint('email_verifier', __name__)

# Email verification route - API endpoint
@email_verifier_bp.route('/verify-emails', methods=['POST'])
@login_required
def api_verify_email():
    """
    API endpoint to verify a Webook account email.
    
    Required POST parameters:
    - email: Account email
    - password: Account password
    - access_token: Current access token
    
    Returns:
    - JSON with success message and new token
    - JSON with error message on failure
    """
    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided", "status": "error"}), 400
    
    email = data.get('email')
    password = data.get('password')
    access_token = data.get('access_token')
    
    if not email or not password or not access_token:
        return jsonify({"error": "Email, password, and access token are required", "status": "error"}), 400
    
    try:
        # Run the async verification function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success, result = loop.run_until_complete(
            verify_webook_account(email, password, access_token)
        )
        loop.close()
        
        if success:
            # Update account verification status
            account = Account.query.filter_by(email=email).first()
            if account:
                account.is_verified = True
                account.updated_at = datetime.utcnow()
                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"Error updating verification status: {str(e)}")
                    # Continue anyway - the verification was successful even if DB update failed
            
            return jsonify({
                "status": "success",
                "email": email,
                "message": "Email verification completed successfully",
                "new_access_token": result
            })
        else:
            logger.error(f"Verification failed for {email}: {result}")
            return jsonify({
                "status": "error",
                "email": email,
                "error": result
            })
    except Exception as e:
        logger.exception(f"Exception during verification for {email}: {str(e)}")
        return jsonify({
            "status": "error",
            "email": email,
            "error": str(e)
        })

# Bulk verification route - API endpoint
@email_verifier_bp.route('/api/verify-emails', methods=['POST'])
@login_required
def api_verify_emails_bulk():
    """
    Enhanced API endpoint to verify multiple Webook account emails in bulk.
    
    Required POST parameters:
    - accounts: List of account objects with email, password, and access_token fields
    
    Optional parameters:
    - max_attempts: Maximum number of verification attempts per account (default: 3)
    - skip_verified: Whether to skip already verified accounts (default: true)
    
    Returns:
    - JSON with results for each account
    """
    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided", "status": "error"}), 400
    
    accounts = data.get('accounts')
    max_attempts = data.get('max_attempts', 3)
    skip_verified = data.get('skip_verified', True)
    
    if not accounts or not isinstance(accounts, list):
        return jsonify({"error": "Invalid accounts data", "status": "error"}), 400
    
    # Prepare results container
    results = []
    
    # Process each account
    for account_data in accounts:
        email = account_data.get('email')
        password = account_data.get('password')
        access_token = account_data.get('access_token')
        
        if not email or not password or not access_token:
            results.append({
                "email": email if email else "Unknown",
                "status": "error",
                "error": "Missing email, password, or access token"
            })
            continue
        
        # Check if account is already verified
        account = Account.query.filter_by(email=email).first()
        
        if account and account.is_verified and skip_verified:
            results.append({
                "email": email,
                "status": "skipped",
                "message": "Account is already verified",
                "is_verified": True
            })
            continue
        
        try:
            # Run the async verification function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success, result = loop.run_until_complete(
                verify_webook_account(email, password, access_token)
            )
            loop.close()
            
            if success:
                # Update account verification status
                db_account = Account.query.filter_by(email=email).first()
                if db_account:
                    db_account.is_verified = True
                    db_account.updated_at = datetime.utcnow()
                    try:
                        db.session.commit()
                    except Exception as e:
                        db.session.rollback()
                        logger.error(f"Error updating verification status: {str(e)}")
                        # Continue anyway - the verification was successful even if DB update failed
                        
                results.append({
                    "email": email,
                    "status": "success",
                    "new_access_token": result,
                    "is_verified": True
                })
            else:
                logger.error(f"Verification failed for {email}: {result}")
                results.append({
                    "email": email,
                    "status": "error",
                    "error": result,
                    "is_verified": False
                })
        except Exception as e:
            logger.exception(f"Exception during verification for {email}: {str(e)}")
            results.append({
                "email": email,
                "status": "error",
                "error": str(e),
                "is_verified": False
            })
    
    return jsonify({
        "status": "complete",
        "total": len(accounts),
        "successful": len([r for r in results if r["status"] == "success"]),
        "failed": len([r for r in results if r["status"] == "error"]),
        "skipped": len([r for r in results if r["status"] == "skipped"]),
        "results": results
    })

# Dashboard route for email verification - Web Interface
@email_verifier_bp.route('/verify-emails', methods=['GET', 'POST'])
@login_required
def verify_emails_dashboard():
    """Enhanced web interface for verifying account emails."""
    if request.method == 'POST':
        # Handle form submission
        email = request.form.get('email')
        password = request.form.get('password')
        account_id = request.form.get('account_id')
        
        # Get account from database
        account = Account.query.get(account_id)
        
        if not account:
            flash('Account not found', 'danger')
            return redirect(url_for('email_verifier.verify_emails_dashboard'))
        
        # Check if we have all required information
        if not email or not password or not account.access_token:
            flash('Email, password, and valid access token are required', 'danger')
            return redirect(url_for('email_verifier.verify_emails_dashboard'))
        
        # Run verification process
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success, result = loop.run_until_complete(
                verify_webook_account(email, password, account.access_token)
            )
            loop.close()
            
            if success:
                # Update account verification status
                account.is_verified = True
                account.updated_at = datetime.utcnow()
                
                # Update access token
                account.access_token = result
                account.token_retrieved_at = datetime.utcnow()
                
                try:
                    db.session.commit()
                    flash('Email verification completed successfully', 'success')
                except Exception as e:
                    db.session.rollback()
                    flash(f'Verification succeeded but database update failed: {str(e)}', 'warning')
            else:
                logger.error(f"Verification failed for {email}: {result}")
                flash(f'Verification failed: {result}', 'danger')
        except Exception as e:
            logger.exception(f"Exception during verification for {email}: {str(e)}")
            flash(f'Error during verification process: {str(e)}', 'danger')
        
        return redirect(url_for('email_verifier.verify_emails_dashboard'))
    
    # GET request - render form
    # Get accounts with valid tokens
    accounts = Account.query.filter(
        Account.is_dead == False,
        Account.access_token.isnot(None)
    ).order_by(Account.id.desc()).limit(50).all()
    
    # Get verification emails
    verification_emails = VerificationEmail.query.filter(
        VerificationEmail.is_active == True,
        VerificationEmail.is_burned == False
    ).order_by(VerificationEmail.id.desc()).all()
    
    # Calculate stats
    total_accounts = Account.query.count()
    verified_accounts = Account.query.filter_by(is_verified=True).count()
    unverified_accounts = Account.query.filter_by(is_verified=False, is_dead=False).count()
    
    verification_stats = {
        'total': total_accounts,
        'verified': verified_accounts,
        'unverified': unverified_accounts,
        'verification_rate': round(verified_accounts / total_accounts * 100, 1) if total_accounts > 0 else 0
    }
    
    return render_template('verify_emails.html', 
                          accounts=accounts, 
                          verification_emails=verification_emails,
                          verification_stats=verification_stats)

@email_verifier_bp.route('/api/verification-emails/<int:email_id>/test-full-process', methods=['GET'])
@login_required
def test_full_verification_process(email_id):
    """
    API endpoint to test the full verification process with a specific email.
    
    Required query parameters:
    - account_id: ID of the account to test with
    
    Returns:
    - JSON with test results
    """
    account_id = request.args.get('account_id', type=int)
    
    if not account_id:
        return jsonify({
            "status": "error",
            "message": "Account ID is required",
            "steps": []
        })
    
    # Get verification email
    verification_email = VerificationEmail.query.get_or_404(email_id)
    
    # Get account
    account = Account.query.get_or_404(account_id)
    
    # Store test steps
    steps = []
    start_time = datetime.now()
    
    # Test the full verification process
    try:
        # 1. Check if verification email is not in use or burned
        if verification_email.in_use:
            steps.append({
                "name": "Check Email Availability",
                "success": False,
                "message": "Verification email is currently in use."
            })
            return jsonify({
                "status": "error",
                "message": "Verification email is currently in use.",
                "steps": steps
            })
        
        if verification_email.is_burned:
            steps.append({
                "name": "Check Email Status",
                "success": False,
                "message": "Verification email is marked as burned (already registered with Webook)."
            })
            return jsonify({
                "status": "error",
                "message": "Verification email is marked as burned.",
                "steps": steps
            })
        
        steps.append({
            "name": "Check Email Status",
            "success": True,
            "message": "Verification email is available for use."
        })
        
        # 2. Run a minimal verification test
        try:
            # Mark email as in use
            verification_email.in_use = True
            db.session.commit()
            
            # Record start time
            test_start = datetime.now()
            
            # Run the verification
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success, result = loop.run_until_complete(
                verify_webook_account(account.email, account.password, account.access_token)
            )
            loop.close()
            
            # Record end time and duration
            test_end = datetime.now()
            duration = int((test_end - test_start).total_seconds() * 1000)
            
            if success:
                steps.append({
                    "name": "Verification Process",
                    "success": True,
                    "message": "Successfully verified the account.",
                    "duration": duration
                })
                
                # Try to update account token
                try:
                    account.access_token = result
                    account.token_retrieved_at = datetime.utcnow()
                    db.session.commit()
                    
                    steps.append({
                        "name": "Update Account Token",
                        "success": True,
                        "message": "Successfully updated account token in database."
                    })
                except Exception as e:
                    steps.append({
                        "name": "Update Account Token",
                        "success": False,
                        "message": f"Failed to update account token: {str(e)}"
                    })
            else:
                steps.append({
                    "name": "Verification Process",
                    "success": False,
                    "message": f"Verification failed: {result}",
                    "duration": duration
                })
                
                return jsonify({
                    "status": "error",
                    "message": f"Verification process failed: {result}",
                    "steps": steps
                })
        finally:
            # Always release the email
            verification_email.in_use = False
            verification_email.last_used = datetime.utcnow()
            db.session.commit()
        
        # 3. Final success
        total_duration = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return jsonify({
            "status": "success",
            "message": f"Test completed successfully in {total_duration}ms.",
            "steps": steps
        })
            
    except Exception as e:
        logger.exception(f"Error during full verification test: {str(e)}")
        steps.append({
            "name": "Test Execution",
            "success": False,
            "message": f"Error: {str(e)}"
        })
        
        # Ensure the email is marked as not in use
        try:
            verification_email.in_use = False
            db.session.commit()
        except:
            pass
        
        return jsonify({
            "status": "error",
            "message": f"Test failed: {str(e)}",
            "steps": steps
        })

@email_verifier_bp.route('/api/verification-emails/<int:email_id>/test-email-check', methods=['GET'])
@login_required
def test_email_check(email_id):
    """
    API endpoint to test email checking functionality.
    
    This will connect to the IMAP server and check for emails in the inbox.
    
    Returns:
    - JSON with test results
    """
    # Get verification email
    verification_email = VerificationEmail.query.get_or_404(email_id)
    
    try:
        import imaplib
        import email
        from email.parser import BytesParser
        from email.policy import default
        
        # Connect to IMAP server
        mail = imaplib.IMAP4_SSL(verification_email.imap_server, verification_email.imap_port)
        mail.login(verification_email.email, verification_email.password)
        
        # Select inbox
        mail.select('INBOX')
        
        # Search for emails
        result, data = mail.search(None, 'ALL')
        
        if result != 'OK':
            mail.logout()
            return jsonify({
                "status": "error",
                "message": "Failed to search for emails"
            })
        
        # Count emails
        email_ids = data[0].split()
        email_count = len(email_ids)
        
        # Get the latest email if available
        latest_email_subject = None
        if email_ids:
            latest_id = email_ids[-1]
            result, msg_data = mail.fetch(latest_id, '(RFC822)')
            
            if result == 'OK':
                msg = BytesParser(policy=default).parsebytes(msg_data[0][1])
                latest_email_subject = msg.get('Subject', 'No Subject')
        
        # Logout
        mail.logout()
        
        return jsonify({
            "status": "success",
            "message": f"Successfully connected to IMAP server and found {email_count} emails in inbox. " + 
                       (f"Latest email subject: '{latest_email_subject}'" if latest_email_subject else "No emails found.")
        })
        
    except Exception as e:
        logger.exception(f"Error during email check test: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Error checking emails: {str(e)}"
        })

# API route to check status of verification emails
@email_verifier_bp.route('/api/verification-emails/status', methods=['GET'])
@login_required
def verification_emails_status():
    """
    API endpoint to get status of verification emails.
    
    Returns:
    - JSON with verification email statistics
    """
    # Get all verification emails
    emails = VerificationEmail.query.all()
    
    total = len(emails)
    active = sum(1 for email in emails if email.is_active and not email.is_burned)
    burned = sum(1 for email in emails if email.is_burned)
    in_use = sum(1 for email in emails if email.in_use)
    
    # Calculate success rate
    success_count = sum(email.success_count for email in emails)
    total_verifications = sum(email.verification_count for email in emails)
    
    success_rate = 0
    if total_verifications > 0:
        success_rate = round((success_count / total_verifications) * 100, 2)
    
    return jsonify({
        "status": "success",
        "stats": {
            "total": total,
            "active": active,
            "burned": burned,
            "in_use": in_use,
            "success_count": success_count,
            "total_verifications": total_verifications,
            "success_rate": success_rate
        }
    })