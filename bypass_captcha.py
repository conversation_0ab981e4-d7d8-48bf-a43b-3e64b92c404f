import asyncio
import httpx
import re
import subprocess
import platform # To adjust help file path if needed
import os
from dotenv import load_dotenv
from urllib.parse import urlencode, urlparse, parse_qsl, urlunparse

# Load environment variables
load_dotenv()

# Get proxy settings
DEFAULT_PROXY = os.getenv('DEFAULT_PROXY', 'ucegtvkm-rotate:<EMAIL>:80')

def get_proxy_url():
    """
    Parse the proxy string and return a URL for httpx.
    Format expected: username:password@host:port
    """
    if not DEFAULT_PROXY:
        return None

    try:
        if '@' in DEFAULT_PROXY:
            auth, host_port = DEFAULT_PROXY.split('@')
            username, password = auth.split(':')
            host, port = host_port.split(':')

            proxy_url = f"http://{username}:{password}@{host}:{port}"
        else:
            # No authentication in proxy
            proxy_url = f"http://{DEFAULT_PROXY}"

        print(f"Using proxy for captcha solving")
        return proxy_url
    except Exception as e:
        print(f"Error parsing proxy: {str(e)}")
        return None

# Helper function to replicate C# QueryHelpers.AddQueryString behaviour robustly
# Although httpx params argument usually handles this well for GET.
def add_query_string(base_url: str, params: dict) -> str:
    """Appends query parameters to a URL."""
    # Ensure base_url doesn't already have conflicting params - simplistic example
    url_parts = list(urlparse(base_url))
    query = dict(parse_qsl(url_parts[4]))
    query.update(params)
    url_parts[4] = urlencode(query)
    return urlunparse(url_parts)

# Equivalent of the ShowHelp method
def show_help():
    """Launches the help file."""
    # Adjust the path based on your environment and OS
    # Assuming the script runs from a location where this relative path is valid
    help_file_path = ".\\UserData\\Plugins\\helpfile.exe" # Windows path
    if platform.system() != "Windows":
        print("Warning: Cannot launch .exe help file on non-Windows OS.")
        # Potentially adapt path or use a different mechanism for help
        # For example: help_file_path = "./UserData/Plugins/helpfile" (if it's a script)
        return

    try:
        print(f"Attempting to launch: {help_file_path}")
        # Use subprocess.run for simplicity, or Popen for non-blocking
        subprocess.run([help_file_path], check=False) # check=False mimics Process.Start behaviour (doesn't throw if exit code != 0)
    except FileNotFoundError:
        print(f"Error: Help file not found at {help_file_path}")
    except Exception as e:
        print(f"Error launching help file: {e}")

# Equivalent of the first Recaptcha method
async def recaptcha(
    ar: str, sitekey: str, co: str, hl: str, v: str,
    size: str, action: str, cb: str, anchor: str,
    reload_url: str, useragent: str
) -> str | None:
    """
    Mimics the first Recaptcha method to get g-recaptcha-response.

    Args:
        ar: Anchor request parameter 'ar'.
        sitekey: Recaptcha sitekey ('k').
        co: Anchor request parameter 'co'.
        hl: Anchor request parameter 'hl' (language).
        v: Anchor request parameter 'v' (version).
        size: Anchor request parameter 'size'.
        action: Anchor request parameter 'sa' (site action).
        cb: Anchor request parameter 'cb' (callback).
        anchor: The base URL for the anchor request (e.g., google.com/recaptcha/api2/anchor).
        reload_url: The base URL for the reload request (e.g., google.com/recaptcha/api2/reload).
        useragent: The User-Agent string for requests.

    Returns:
        The extracted g-recaptcha-response string, or None if an error occurs.
    """
    anchor_params = {
        "ar": ar,
        "k": sitekey,
        "co": co,
        "hl": hl,
        "v": v,
        "size": size,
        "sa": action,
        "cb": cb,
    }
    # new_url = add_query_string(anchor, anchor_params) # Or let httpx handle params
    headers = {"User-Agent": useragent}

    proxy = get_proxy_url()
    async with httpx.AsyncClient(proxies=proxy) as client:
        try:
            # --- First Request (GET anchor) ---
            # print(f"GET: {anchor} with params: {anchor_params}")
            response = await client.get(anchor, params=anchor_params, headers=headers)
            response.raise_for_status() # Check for HTTP errors
            result_html = response.text
            # print(f"Anchor Response HTML (snippet): {result_html[:500]}") # Optional: Debugging

            # --- Parse recaptcha-token (using string finding - fragile) ---
            token_key = "recaptcha-token"
            start_index = result_html.find(token_key)
            if start_index == -1:
                print(f"Error: '{token_key}' not found in anchor response.")
                return None

            # Adjust index to start of value attribute's content
            value_start_marker = 'value="'
            value_start_index = result_html.find(value_start_marker, start_index)
            if value_start_index == -1:
                 print(f"Error: Could not find value attribute for {token_key}.")
                 return None

            first_char_index = value_start_index + len(value_start_marker)

            # Find the closing quote
            last_char_index = result_html.find('"', first_char_index)
            if last_char_index == -1:
                 print(f"Error: Could not find closing quote for {token_key} value.")
                 return None

            recaptcha_token = result_html[first_char_index:last_char_index]
            # Old C# logic reference:
            # firstStringPosition = result.IndexOf("recaptcha-token") + 24; # Very specific offset
            # lastPosition = result.IndexOf(">", firstStringPosition) - 1; # Looks for end of tag?
            # presult = result.Substring(firstStringPosition, lastPosition - firstStringPosition); # Fragile
            # print(f"Extracted recaptcha-token: {recaptcha_token}") # Optional: Debugging


            if not recaptcha_token:
                 print("Error: Failed to extract recaptcha-token.")
                 return None

            # --- Second Request (POST reload) ---
            reload_params = {
                "k": sitekey,
                "c": recaptcha_token,
                "reason": "q", # Hardcoded in C#
                # The C# code adds these params to the URL *and* sends them as form data.
                # Usually only one is needed. POST data is more common for reload.
            }
            print(f"POST: {reload_url} with data: {reload_params}")
            # Send as form data
            response2 = await client.post(reload_url, data=reload_params, headers=headers)
            response2.raise_for_status()
            result2_text = response2.text
            # print(f"Reload Response Text (snippet): {result2_text[:500]}") # Optional: Debugging

            # --- Parse g-recaptcha-response (using string finding - fragile) ---
            response_key = '"rresp"' # Looking for key in what looks like JS/JSON response
            start_index2 = result2_text.find(response_key)
            if start_index2 == -1:
                 print(f"Error: '{response_key}' not found in reload response.")
                 return None

            # Find the start of the value after the comma and quote
            value_start_marker2 = ',"'
            value_start_index2 = result2_text.find(value_start_marker2, start_index2 + len(response_key))
            if value_start_index2 == -1:
                print(f"Error: Could not find start of value for {response_key}.")
                return None

            first_char_index2 = value_start_index2 + len(value_start_marker2)

            # Find the closing quote
            last_char_index2 = result2_text.find('"', first_char_index2)
            if last_char_index2 == -1:
                print(f"Error: Could not find closing quote for {response_key} value.")
                return None

            grecaptcha_response = result2_text[first_char_index2:last_char_index2]
            # Old C# logic reference:
            # firstStringPosition2 = result2.IndexOf("rresp") + 8; # Specific offset
            # lastPosition2 = result2.IndexOf(",", firstStringPosition2) - 1; # Looks for comma separator?
            # presult2 = result2.Substring(firstStringPosition2, lastPosition2 - firstStringPosition2); # Fragile

            # print(f"Extracted g-recaptcha-response: {grecaptcha_response}")
            return grecaptcha_response

        except httpx.RequestError as e:
            print(f"An HTTP error occurred: {e}")
            return None
        except httpx.HTTPStatusError as e:
            print(f"HTTP request failed with status {e.response.status_code}: {e.response.text[:500]}...")
            return None
        except Exception as e:
            print(f"An unexpected error occurred in recaptcha: {e}")
            return None


# Equivalent of the second Recaptcha1 method
async def recaptcha1(
    captcha_url: str, enterprise: bool, useragent: str
) -> str | None:
    """
    Mimics the second Recaptcha1 method using regex parsing.

    Args:
        captcha_url: The full URL to the initial captcha page/frame.
        enterprise: Boolean flag indicating if it's reCAPTCHA Enterprise.
        useragent: The User-Agent string for requests.

    Returns:
        The extracted g-recaptcha-response string, or None if an error occurs.
    """
    headers = {"User-Agent": useragent}

    proxy = get_proxy_url()
    async with httpx.AsyncClient(proxies=proxy) as client:
        try:
            # --- First Request (GET captcha_url) ---
            # print(f"GET: {captcha_url}")
            response = await client.get(captcha_url, headers=headers)
            response.raise_for_status()
            result_html = response.text
            # print(f"Captcha URL Response HTML (snippet): {result_html[:500]}") # Optional: Debugging

            # --- Parse recaptcha-token using Regex ---
            token_match = re.search(r'id="recaptcha-token"\s+value="(.*?)"', result_html, re.IGNORECASE)
            if not token_match:
                print("Error: Could not find recaptcha-token using regex.")
                return None
            recaptcha_token = token_match.group(1)
            # print(f"Extracted recaptcha-token: {recaptcha_token}") # Optional: Debugging

            # --- Extract sitekey (k) from captcha_url using Regex ---
            sitekey_match = re.search(r'[?&]k=([^&]+)', captcha_url)
            if not sitekey_match:
                print("Error: Could not extract sitekey (k=...) from captcha_url.")
                return None
            sitekey = sitekey_match.group(1)
            # print(f"Extracted sitekey: {sitekey}") # Optional: Debugging

            # --- Determine reload URL ---
            if enterprise:
                reload_base_url = "https://www.google.com/recaptcha/enterprise/reload"
            else:
                reload_base_url = "https://www.google.com/recaptcha/api2/reload"

            # --- Second Request (POST reload) ---
            reload_params = {
                "k": sitekey,
                "c": recaptcha_token,
                "reason": "q", # Hardcoded in C#
            }
            # Build the full URL with query parameters for the POST request target
            # (Though parameters are sent in body, the URL often includes them too for reload)
            # Uncomment if needed for debugging or alternative implementation
            # reload_url_with_params = add_query_string(reload_base_url, reload_params)

            # print(f"POST: {reload_base_url} with data: {reload_params}") # Post to base, send params as data
            response2 = await client.post(reload_base_url, data=reload_params, headers=headers)
            # Alternative C# interpretation: POST to URL with params in query string *and* body?
            # response2 = await client.post(reload_url_with_params, data=reload_params, headers=headers)
            response2.raise_for_status()
            result2_text = response2.text
            # print(f"Reload Response Text (snippet): {result2_text[:500]}") # Optional: Debugging

            # --- Parse g-recaptcha-response using Regex ---
            response_match = re.search(r'"rresp","(.*?)"', result2_text)
            if not response_match:
                print("Error: Could not extract g-recaptcha-response ('rresp') using regex.")
                return None
            grecaptcha_response = response_match.group(1)

            # print(f"Extracted g-recaptcha-response: {grecaptcha_response}")
            return grecaptcha_response

        except httpx.RequestError as e:
            print(f"An HTTP error occurred: {e}")
            return None
        except httpx.HTTPStatusError as e:
            print(f"HTTP request failed with status {e.response.status_code}: {e.response.text[:500]}...")
            return None
        except Exception as e:
            print(f"An unexpected error occurred in recaptcha1: {e}")
            return None

# --- Example Usage ---
async def main():
    print("--- Testing show_help() ---")
    # show_help() # Uncomment to test launching the help file

    print("\n--- Testing recaptcha() ---")
    # Replace with actual valid parameters for testing
    g_response_1 = await recaptcha(
        ar="1",
        sitekey="6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-", # Example sitekey
        co="_PUT_CO_HERE_", # e.g., a country code like 'US' or the full co value
        hl="en",
        v="_PUT_VERSION_HERE_", # e.g., 'WgF98Wgde0x02GQTm1fTsWNw'
        size="invisible",
        action="", # Optional action name if used
        cb="...", # Callback function name
        anchor="https://www.google.com/recaptcha/api2/anchor",
        reload_url="https://www.google.com/recaptcha/api2/reload",
        useragent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.0.0 Safari/537.36" # Example
    )
    if g_response_1:
        print(f"Recaptcha Result 1: Success (token length: {len(g_response_1)})")
    else:
        print("Recaptcha Result 1: Failed")


    print("\n--- Testing recaptcha1() ---")
     # Construct a realistic captcha URL (replace with an actual one if possible)
    example_sitekey = "6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-"
    example_version = "WgF98Wgde0x02GQTm1fTsWNw"
    example_co = "US" # Example
    example_hl = "en"
    example_url = f"https://www.google.com/recaptcha/api2/anchor?ar=1&k={example_sitekey}&co={example_co}&hl={example_hl}&v={example_version}&size=invisible&cb=examplecb"

    g_response_2 = await recaptcha1(
        captcha_url=example_url,
        enterprise=False,
        useragent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.0.0 Safari/537.36" # Example
    )
    if g_response_2:
        print(f"Recaptcha Result 2: Success (token length: {len(g_response_2)})")
    else:
        print("Recaptcha Result 2: Failed")


if __name__ == "__main__":
    # Make sure httpx is installed: pip install httpx
    # Run the async main function
    asyncio.run(main())