# models.py
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime, timedelta

# Initialize SQLAlchemy without binding to an app yet
db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.<PERSON>an, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Account(db.Model):
    __tablename__ = 'accounts'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    access_token = db.Column(db.Text, nullable=True)
    token_retrieved_at = db.Column(db.DateTime, nullable=True)
    is_dead = db.Column(db.Boolean, default=False)
    is_verified = db.Column(db.Boolean, default=False)  # New field for verification status
    last_login_attempt = db.Column(db.DateTime, nullable=True)
    login_attempts = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Account {self.email}>'
    
    # Add all your Account methods here (like to_dict, is_token_valid, etc.)
    def is_token_valid(self):
        """Check if the token is still valid (less than 7 days old)."""
        if not self.access_token or not self.token_retrieved_at:
            return False

        now = datetime.utcnow()
        token_age = (now - self.token_retrieved_at).total_seconds()
        # Use a hardcoded value since we can't access app.config here
        return token_age < 7 * 24 * 60 * 60  # 7 days in seconds
        
    def to_dict(self):
        result = {
            'id': self.id,
            'email': self.email,
            'password': self.password,
            'is_dead': self.is_dead,
            'is_verified': self.is_verified,  # Include verification status
            'access_token': self.access_token,
            'token_retrieved_at': self.token_retrieved_at.isoformat() if self.token_retrieved_at else None,
            'token_valid': self.is_token_valid(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # Add token expiration information
        result.update(self.get_token_expiration())

        return result
        
    def get_token_expiration(self):
        """Get token expiration information."""
        if not self.access_token or not self.token_retrieved_at:
            return {
                "valid": False,
                "expires_in_seconds": 0,
                "expires_at_epoch": 0
            }
            
        now = datetime.utcnow()
        token_age = (now - self.token_retrieved_at).total_seconds()
        validity_period = 7 * 24 * 60 * 60  # 7 days in seconds
        
        expires_in = validity_period - token_age
        expires_at = (self.token_retrieved_at + timedelta(seconds=validity_period)).timestamp()
        
        return {
            "valid": expires_in > 0,
            "expires_in_seconds": max(0, int(expires_in)),
            "expires_at_epoch": int(expires_at)
        }


class Setting(db.Model):
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @classmethod
    def get(cls, key, default=None):
        """Get a setting value by key."""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            return setting.value
        return default

    @classmethod
    def set(cls, key, value):
        """Set a setting value."""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = value
        else:
            setting = cls(key=key, value=value)
            db.session.add(setting)
        db.session.commit()

    def __repr__(self):
        return f'<Setting {self.key}>'
    
class VerificationEmail(db.Model):
    __tablename__ = 'verification_emails'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    refresh_token = db.Column(db.Text, nullable=True)
    client_id = db.Column(db.String(255), nullable=True)

    imap_server = db.Column(db.String(255), nullable=False)
    imap_port = db.Column(db.Integer, default=993)
    smtp_server = db.Column(db.String(255), nullable=True)
    smtp_port = db.Column(db.Integer, default=587)
    is_active = db.Column(db.Boolean, default=True)
    in_use = db.Column(db.Boolean, default=False)  # Flag to indicate if email is currently being used
    is_burned = db.Column(db.Boolean, default=False)  # Flag for emails that can't be used (already registered)
    last_used = db.Column(db.DateTime, nullable=True)
    verification_count = db.Column(db.Integer, default=0)  # Counter for verifications performed
    success_count = db.Column(db.Integer, default=0)  # Counter for successful verifications
    failure_count = db.Column(db.Integer, default=0)  # Counter for failed verifications
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<VerificationEmail {self.email}>'

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'refresh_token': self.refresh_token,
            'client_id': self.client_id,
            'imap_server': self.imap_server,
            'imap_port': self.imap_port,
            'smtp_server': self.smtp_server,
            'smtp_port': self.smtp_port,
            'is_active': self.is_active,
            'is_burned': self.is_burned,
            'verification_count': self.verification_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': self.get_success_rate(),
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
    def get_success_rate(self):
        """Calculate verification success rate."""
        if self.verification_count == 0:
            return 0
        return round((self.success_count / self.verification_count) * 100, 2)