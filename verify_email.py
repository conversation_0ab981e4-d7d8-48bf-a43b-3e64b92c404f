#!/usr/bin/env python3
"""
Webook Email Verification Script

This script automates the email verification process for Webook accounts.
The process includes:
1. Changing the account email to an email address we have access to
2. Getting a new token after email update
3. Requesting a verification email
4. Retrieving the OTP from the email inbox
5. Submitting the verification code
6. Changing the email back to the original

Setup:
1. Install requirements:
   pip install requests imaplib email python-dotenv pymysql

2. Create a .env file with:
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=your-app-password
   EMAIL_IMAP_SERVER=imap.example.com
   EMAIL_IMAP_PORT=993
   SITE_TOKEN=e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2
   DB_HOST=localhost
   DB_USER=webook_user
   DB_PASSWORD=webook_password
   DB_NAME=webook_db


   # Common IMAP servers:
   # Outlook.com: outlook.office365.com (port 993)
   # Yahoo Mail: imap.mail.yahoo.com (port 993)
   # AOL: imap.aol.com (port 993)
   # Zoho Mail: imap.zoho.com (port 993)
"""
import logging
import os
import re
import time
import json
import random
import hashlib
import imaplib
import email
import email.header
from email.parser import BytesParser
from email.policy import default
import requests
import asyncio
from dotenv import load_dotenv
import argparse
import datetime
import pymysql
from bypass_captcha import recaptcha1 # Assuming this is in the same directory or PYTHONPATH
# Assuming models.py might not be directly importable in a standalone script context,
# but VerificationEmail structure is needed.
# from models import VerificationEmail # This might fail if script run outside Flask context

# Load environment variables
load_dotenv()

# Configuration for verification emails (placeholders, fetched from DB ideally)
# These are fallback if DB fetch fails or for standalone use without full Flask context.
EMAIL_ADDRESS = os.getenv('EMAIL_ADDRESS')
EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
EMAIL_IMAP_SERVER = os.getenv('EMAIL_IMAP_SERVER')
EMAIL_IMAP_PORT = int(os.getenv('EMAIL_IMAP_PORT', '993'))
SITE_TOKEN = os.getenv('SITE_TOKEN', 'e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2')

# Proxy settings
DEFAULT_PROXY = os.getenv('DEFAULT_PROXY', 'ucegtvkm-rotate:<EMAIL>:80')


# Database connection settings from .env
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_USER = os.getenv('DB_USER', 'webook_user')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'webook_password')
DB_NAME = os.getenv('DB_NAME', 'webook_db')

# Wait time between operations (seconds)
WAIT_TIME = 0.2 # Increased slightly

# Setup basic logging if not configured by Flask app
logger = logging.getLogger(__name__)
if not logger.hasHandlers():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define a simple class to mimic the VerificationEmail model structure if models.py isn't available
class SimpleVerificationEmail:
    def __init__(self, id, email, password, refresh_token, client_id, imap_server, imap_port, **kwargs):
        self.id = id
        self.email = email
        self.password = password
        self.refresh_token = refresh_token
        self.client_id = client_id
        self.imap_server = imap_server
        self.imap_port = imap_port
        # Add other fields if necessary, possibly from kwargs


def get_proxy_dict():
    """
    Parse the proxy string and return a dictionary for requests.
    Format expected: username:password@host:port or host:port
    """
    if not DEFAULT_PROXY:
        return None
    try:
        if '@' in DEFAULT_PROXY:
            auth, host_port = DEFAULT_PROXY.split('@', 1)
            if ':' in auth:
                username, password = auth.split(':', 1)
            else: # Should not happen with user:pass@host:port
                logger.warning(f"Proxy string '{DEFAULT_PROXY}' has '@' but no ':' in auth part.")
                return None
            
            if ':' in host_port:
                host, port = host_port.split(':', 1)
            else:
                logger.warning(f"Proxy string '{DEFAULT_PROXY}' has '@' but no ':' in host_port part.")
                return None

            proxy_url = f'http://{username}:{password}@{host}:{port}'
        else: # No authentication in proxy
            if ':' not in DEFAULT_PROXY:
                logger.warning(f"Proxy string '{DEFAULT_PROXY}' seems malformed (no auth, no port).")
                return None
            # Assuming format is host:port
            proxy_url = f'http://{DEFAULT_PROXY}'
            host, port = DEFAULT_PROXY.split(':', 1)


        logger.info(f"Using proxy: {host}:{port}")
        return {'http': proxy_url, 'https': proxy_url}
    except ValueError as e: # More specific exception for split errors
        logger.error(f"Error parsing proxy string '{DEFAULT_PROXY}': {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error parsing proxy: {e}")
        return None

async def random_user_agent():
    """Generate a user agent."""
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"

async def generate_signature(email_to_sign): # Renamed parameter for clarity
    """Generate a signature for API requests."""
    random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=20))
    md5_hash = hashlib.md5(f"{email_to_sign}{random_str}".encode()).hexdigest()
    return f"{md5_hash}|{random_str}"

async def solve_captcha():
    """Solve captcha using the bypass_captcha module."""
    user_agent = await random_user_agent()
    captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcvYHooAAAAAC-G46bpymJKtIwfDQpg9DsHPMpL&co=aHR0cHM6Ly93ZWJvb2suY29tOjQ0Mw..&hl=en&v=IyZ984yGrXrBd6ihLOYGwy9X&size=invisible&cb=b7e5fsomtcd"

    logger.info("Solving captcha...")
    cap = await recaptcha1(captcha_url, False, user_agent)

    if not cap:
        logger.error("Failed to solve captcha.")
        return None

    logger.info("Captcha solved successfully.")
    return cap

async def update_email(access_token, new_email_address, original_webook_email_for_sig): # Added original email for consistent signature generation
    """Update the email address of the account."""
    captcha = await solve_captcha()
    if not captcha:
        return False, "Failed to solve captcha for email update"

    # Signature should ideally be based on a consistent identifier,
    # using the original Webook email might be more stable if 'new_email_address' changes during retries.
    signature = await generate_signature(new_email_address)
    user_agent = await random_user_agent()

    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': f'Bearer {access_token}',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'token': SITE_TOKEN,
        'user-agent': user_agent,
    }

    json_data = {
        'new_email': new_email_address,
        'lang': 'en',
        'signature': signature,
        'captcha': captcha,
        'app_source': 'rs',
    }

    logger.info(f"Updating email to: {new_email_address}")
    proxies = get_proxy_dict()
    try:
        response = requests.post(
            'https://api.webook.com/api/v2/update-email',
            headers=headers,
            json=json_data,
            proxies=proxies,
            timeout=30,
        )
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        response_data = response.json()
        if response_data.get('status') == 'success': # Check for explicit success status
            logger.info("Email updated successfully.")
            return True, response_data
        else:
            error_msg = response_data.get('message', 'Unknown error during email update')
            logger.error(f"Email update failed: {error_msg} - Response: {response_data}")
            return False, error_msg
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException during email update: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Error processing email update response: {e}")
        return False, str(e)


async def login_after_update(email_to_login, password_to_login): # Renamed parameters
    """Login after email update to get new access token."""
    captcha = await solve_captcha()
    if not captcha:
        return False, "Failed to solve captcha for login"

    signature = await generate_signature(email_to_login)
    user_agent = await random_user_agent()

    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': 'Bearer', # Typically empty or placeholder for login
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'token': SITE_TOKEN,
        'user-agent': user_agent,
    }

    json_data = {
        'email': email_to_login,
        'password': password_to_login,
        'captcha': captcha,
        'signature': signature,
        'app_source': 'rs',
        'login_with': 'email',
        'lang': 'en',
    }

    logger.info(f"Logging in with email: {email_to_login}")
    proxies = get_proxy_dict()
    try:
        response = requests.post(
            'https://api.webook.com/api/v2/login',
            headers=headers,
            json=json_data,
            proxies=proxies,
            timeout=30,
        )
        response.raise_for_status()
        response_data = response.json()
        if response_data.get('data', {}).get('access_token'):
            access_token = response_data['data']['access_token']
            logger.info("Login successful. New access token retrieved.")
            return True, access_token
        else:
            error_msg = response_data.get('message', 'Unknown error during login')
            logger.error(f"Login failed: {error_msg} - Response: {response_data}")
            return False, error_msg
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException during login: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Error processing login response: {e}")
        return False, str(e)

async def send_verification_email_request(access_token_for_request): # Renamed parameter
    """Request a verification email."""
    captcha = await solve_captcha()
    if not captcha:
        return False, "Failed to solve captcha for sending verification email"

    user_agent = await random_user_agent()
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': f'Bearer {access_token_for_request}',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'token': SITE_TOKEN,
        'user-agent': user_agent,
    }
    json_data = {'lang': 'en', 'captcha': captcha, 'app_source': 'rs'}

    logger.info("Requesting verification email...")
    proxies = get_proxy_dict()
    try:
        response = requests.post(
            'https://api.webook.com/api/v2/email_verification/send_email',
            headers=headers,
            json=json_data,
            proxies=proxies,
            timeout=30,
        )
        response.raise_for_status()
        response_data = response.json()
        # Assuming success if no error is raised and status 200.
        # Some APIs might not have a 'status':'success' field for this type of request.
        logger.info(f"Verification email send request successful. Response: {response_data}")
        return True, response_data
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException sending verification email: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Error processing send verification email response: {e}")
        return False, str(e)


def get_verification_code_from_imap(verification_email_obj: SimpleVerificationEmail, max_attempts=5, initial_wait=5, retry_wait=3): # Type hint with SimpleVerificationEmail
    """
    Enhanced function to check email inbox for verification code using provided IMAP credentials.
    """
    logger.info(f"Checking email inbox for verification email: {verification_email_obj.email}")
    time.sleep(initial_wait)
    
    for attempt in range(max_attempts):
        logger.info(f"IMAP check attempt {attempt + 1}/{max_attempts} for {verification_email_obj.email}")
        mail = None # Ensure mail is defined for finally block
        try:
            mail = imaplib.IMAP4_SSL(verification_email_obj.imap_server, verification_email_obj.imap_port, timeout=20) # Added timeout
            if verification_email_obj.refresh_token and verification_email_obj.client_id:
                logger.info("Attempting XOAUTH2 authentication...")
                access_token_oauth = get_access_token_hotmail(verification_email_obj.refresh_token, verification_email_obj.client_id) # Renamed
                if not access_token_oauth:
                    logger.warning("Could not get OAuth access token. Skipping XOAUTH2 attempt.")
                    if attempt < max_attempts - 1: time.sleep(retry_wait); continue
                    else: return False, "Failed to get OAuth token for IMAP"
                
                auth_string = generate_auth_string(verification_email_obj.email, access_token_oauth)
                mail.authenticate("XOAUTH2", lambda x: auth_string)
            else:
                logger.info("Attempting standard IMAP login...")
                mail.login(verification_email_obj.email, verification_email_obj.password)

            mail.select('INBOX')
            
            search_criteria_list = [ # Renamed for clarity
                '(UNSEEN SUBJECT "Use this code to unlock fun")', # More specific first
                '(SUBJECT "Use this code to unlock fun")',
                '(UNSEEN OR SUBJECT "verification" SUBJECT "code" SUBJECT "Webook")',
                '(OR SUBJECT "verification" SUBJECT "code" SUBJECT "Webook")',
                '(UNSEEN)',
                '(ALL)'
            ]
            
            msg_ids = []
            for criteria in search_criteria_list:
                logger.debug(f"Searching IMAP with criteria: {criteria}")
                result, data = mail.search(None, criteria)
                if result == 'OK' and data[0]:
                    msg_ids = data[0].split()
                    if msg_ids:
                        logger.info(f"Found {len(msg_ids)} email(s) with criteria: {criteria}")
                        break 
            
            if not msg_ids:
                logger.info(f"No emails found matching criteria (attempt {attempt+1}/{max_attempts}).")
                if attempt < max_attempts - 1: time.sleep(retry_wait); continue
                else: return False, "No relevant emails found after multiple attempts"

            # Process emails from newest to oldest
            for email_id_bytes in reversed(msg_ids):
                result, msg_data = mail.fetch(email_id_bytes, '(RFC822)')
                if result != 'OK':
                    logger.warning(f"Failed to fetch email ID {email_id_bytes.decode()}.")
                    continue

                msg = BytesParser(policy=default).parsebytes(msg_data[0][1])
                subject_header = email.header.decode_header(msg.get('Subject', 'No Subject'))
                subject = ''.join([str(s, c or 'utf-8') if isinstance(s, bytes) else s for s, c in subject_header])
                logger.info(f"Processing email with subject: {subject}")

                html_content, text_content = None, None
                if msg.is_multipart():
                    for part in msg.walk(): # walk() is better for nested multiparts
                        content_type = part.get_content_type()
                        if content_type == 'text/html' and not html_content: # Get first HTML part
                            try: html_content = part.get_payload(decode=True).decode(part.get_content_charset() or 'utf-8', errors='ignore')
                            except: pass # Ignore decoding errors for now
                        elif content_type == 'text/plain' and not text_content: # Get first text part
                            try: text_content = part.get_payload(decode=True).decode(part.get_content_charset() or 'utf-8', errors='ignore')
                            except: pass
                else: # Not multipart
                    content_type = msg.get_content_type()
                    if content_type == 'text/html':
                        try: html_content = msg.get_payload(decode=True).decode(msg.get_content_charset() or 'utf-8', errors='ignore')
                        except: pass
                    elif content_type == 'text/plain':
                        try: text_content = msg.get_payload(decode=True).decode(msg.get_content_charset() or 'utf-8', errors='ignore')
                        except: pass
                
                content_to_search = html_content or text_content
                if not content_to_search:
                    logger.warning("No text or HTML content found in the email.")
                    continue

                otp_patterns = [
                    r'class="[^"]*otp-box[^"]*"[^>]*>\s*(\d{4,8})\s*<', # Original
                    r'verification code[^<]*?<[^>]+>\s*(\d{4,8})\s*<', # More robust for HTML
                    r'verification code(?: is)?:?\s*<b>\s*(\d{4,8})\s*</b>',
                    r'verification code(?: is)?:?\s*<strong>\s*(\d{4,8})\s*</strong>',
                    r'>\s*(\d{4,8})\s*<', # Generic digits in HTML tags
                    r'Your confirmation code is:?\s*(\d{4,8})', # Common text pattern
                    r'Webook code:?\s*(\d{4,8})',
                    r'\b(\d{4,8})\b' # Generic 4-8 digits as a fallback
                ]
                
                for pattern in otp_patterns:
                    match = re.search(pattern, content_to_search, re.IGNORECASE)
                    if match:
                        otp = match.group(1)
                        logger.info(f"Verification code found: {otp} using pattern: {pattern}")
                        mail.store(email_id_bytes, '+FLAGS', '\\Deleted') # Mark for deletion
                        mail.expunge()
                        return True, otp
            
            logger.info(f"No OTP found in processed emails for attempt {attempt+1}.")
            if attempt < max_attempts - 1: time.sleep(retry_wait)

        except imaplib.IMAP4.error as e: # Catch IMAP specific errors
            logger.error(f"IMAP Error (attempt {attempt+1}): {e}")
            if "authentication failed" in str(e).lower() and verification_email_obj.refresh_token:
                 logger.warning("XOAUTH2 authentication failed. Consider checking refresh token or app permissions.")
            if attempt < max_attempts - 1: time.sleep(retry_wait)
            else: return False, f"IMAP error: {e}"
        except Exception as e:
            logger.error(f"Error checking for verification code (attempt {attempt+1}): {e}", exc_info=True)
            if attempt < max_attempts - 1: time.sleep(retry_wait)
            else: return False, f"General error during IMAP: {e}"
        finally:
            if mail:
                try: mail.close()
                except: pass
                try: mail.logout()
                except: pass
    
    return False, "Failed to find verification code after multiple attempts and patterns."


async def verify_email_with_code_submission(access_token_for_submit, code_to_submit): # Renamed parameters
    """Submit the verification code."""
    captcha = await solve_captcha()
    if not captcha:
        return False, "Failed to solve captcha for email verification"

    user_agent = await random_user_agent()
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': f'Bearer {access_token_for_submit}',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://webook.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'token': SITE_TOKEN,
        'user-agent': user_agent,
    }
    json_data = {'code': code_to_submit, 'lang': 'en', 'captcha': captcha}

    logger.info(f"Submitting verification code: {code_to_submit}")
    proxies = get_proxy_dict()
    try:
        response = requests.post(
            'https://api.webook.com/api/v2/email_verification/verify_email',
            headers=headers,
            json=json_data,
            proxies=proxies,
            timeout=30,
        )
        response.raise_for_status()
        response_data = response.json()
        # Assuming success if 200 OK and no explicit error in response_data
        # Some APIs might return status:"success" here too.
        if response_data.get('status') == 'success' or response.ok: # Check for explicit success or just HTTP 200
            logger.info(f"Email verified successfully with code. Response: {response_data}")
            return True, response_data
        else:
            error_msg = response_data.get('message', 'Unknown error during code submission')
            logger.error(f"Email verification with code failed: {error_msg} - Response: {response_data}")
            return False, error_msg
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException during code submission: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Error processing verification response: {e}")
        return False, str(e)

def get_local_db_account_status(webook_email_address):
    """Check is_verified and access_token from the local accounts table using pymysql."""
    connection = None
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME, cursorclass=pymysql.cursors.DictCursor)
        with connection.cursor() as cursor:
            cursor.execute("SELECT is_verified, access_token FROM accounts WHERE email = %s", (webook_email_address,))
            result = cursor.fetchone()
            if result:
                return result['is_verified'], result['access_token']
            return None, None # Account not found
    except pymysql.Error as e:
        logger.error(f"DB error getting local account status for {webook_email_address}: {e}")
        return None, None # Error condition
    finally:
        if connection:
            connection.close()

async def verify_webook_account(original_webook_email, original_webook_password, current_webook_access_token):
    """
    Run the full email verification process for a single Webook account.
    Uses one verification email from the pool per successful attempt.
    """
    logger.info(f"\n=== Starting verification process for Webook account: {original_webook_email} ===")
    
    # Step 0: Check local DB first if account is already marked as verified
    local_is_verified, local_token = get_local_db_account_status(original_webook_email)
    if local_is_verified == 1: # Assuming 1 for True
        logger.info(f"Account {original_webook_email} is already marked as verified in the local database. Skipping.")
        # Return the token from local DB if available, else the passed one.
        return True, local_token if local_token else current_webook_access_token

    # Step 1: Check Webook API if the account is already verified (could be stale)
    api_is_verified, _ = await check_account_verification_status(current_webook_access_token)
    if api_is_verified: # If API confirms it's verified
        logger.info(f"Account {original_webook_email} is already verified according to Webook API. Marking local DB and skipping.")
        mark_account_as_verified_in_db(original_webook_email) # Ensure local DB is consistent
        return True, current_webook_access_token

    logger.info(f"Account {original_webook_email} is not verified (or status unknown via API). Proceeding with verification...")
    
    verification_emails_pool = get_available_verification_emails_from_db() # Renamed
    if not verification_emails_pool:
        logger.error("No verification emails available in the pool.")
        return False, "No verification emails available for use."
        
    verification_process_successful = False
    final_retrieved_token = current_webook_access_token # Fallback to current token
    last_error_message = "Verification failed with all available pool emails or an initial step failed." # Renamed
    
    for pool_email_obj in verification_emails_pool: # Renamed loop variable
        current_pool_email_id = pool_email_obj.id
        logger.info(f"Attempting verification using pool email: {pool_email_obj.email}")
        
        try:
            update_verification_email_status_in_db(current_pool_email_id, in_use=True) # Renamed
            
            # Step 2: Update Webook account email to the pool email
            update_step_success, update_result_data = await update_email(current_webook_access_token, pool_email_obj.email, original_webook_email) # Renamed
            if not update_step_success:
                last_error_message = f"Failed to update Webook email to {pool_email_obj.email}: {update_result_data}"
                if "already exists" in str(update_result_data).lower(): # Check if pool email is burned
                    logger.warning(f"Pool email {pool_email_obj.email} is already registered with Webook. Marking as burned.")
                    update_verification_email_status_in_db(current_pool_email_id, is_burned=True)
                update_verification_email_stats_in_db(current_pool_email_id, success=False) # Renamed
                continue # Try next pool email

            time.sleep(WAIT_TIME)

            # Step 3: Login to Webook with the pool email to get a new token for OTP request
            login_step_success, new_token_for_otp = await login_after_update(pool_email_obj.email, original_webook_password) # Renamed
            if not login_step_success:
                last_error_message = f"Failed to login to Webook with pool email {pool_email_obj.email}: {new_token_for_otp}"
                await update_email(current_webook_access_token, original_webook_email, original_webook_email) # Attempt to revert
                update_verification_email_stats_in_db(current_pool_email_id, success=False)
                continue

            # Step 4: Send verification email (OTP request) using the new token
            send_step_success, _ = await send_verification_email_request(new_token_for_otp) # Renamed
            if not send_step_success:
                last_error_message = f"Failed to send OTP request for {pool_email_obj.email}"
                await update_email(new_token_for_otp, original_webook_email, original_webook_email) # Attempt to revert with current token
                update_verification_email_stats_in_db(current_pool_email_id, success=False)
                continue

            # Step 5: Get verification code from pool email's inbox
            code_step_success, otp_code = get_verification_code_from_imap(pool_email_obj) # Renamed
            if not code_step_success:
                last_error_message = f"Failed to get OTP from {pool_email_obj.email}: {otp_code}"
                await update_email(new_token_for_otp, original_webook_email, original_webook_email)
                update_verification_email_stats_in_db(current_pool_email_id, success=False)
                continue
            
            # Step 6: Submit the OTP to Webook to verify
            verify_step_success, _ = await verify_email_with_code_submission(new_token_for_otp, otp_code) # Renamed
            if not verify_step_success:
                last_error_message = f"Failed to verify Webook account with OTP {otp_code} from {pool_email_obj.email}"
                await update_email(new_token_for_otp, original_webook_email, original_webook_email)
                update_verification_email_stats_in_db(current_pool_email_id, success=False)
                continue

            # Step 7: Revert Webook account email back to original
            revert_step_success, _ = await update_email(new_token_for_otp, original_webook_email, original_webook_email) # Renamed
            if not revert_step_success:
                last_error_message = f"Critical: Verified, but failed to revert email to {original_webook_email}."
                # Account is verified but email is wrong. Log this as critical.
                # Try to login with original email to get a token anyway.
                update_verification_email_stats_in_db(current_pool_email_id, success=True) # OTP part was successful
                # Proceed to final login, hoping it works.
            
            time.sleep(WAIT_TIME)
            
            # Step 8: Final login to Webook with original email to get the final, verified token
            final_login_success, final_token_value = await login_after_update(original_webook_email, original_webook_password) # Renamed
            if not final_login_success:
                last_error_message = f"Verified, but failed final login to {original_webook_email}: {final_token_value}"
                # Account is verified, but we couldn't get a fresh token.
                # The new_token_for_otp is for the temporary email, so not useful.
                # The original_access_token might be stale.
                update_verification_email_stats_in_db(current_pool_email_id, success=True) # OTP part was successful
                # Mark as overall failure for this pool email if fresh token not obtained.
                # However, the Webook account itself *is* verified.
                mark_account_as_verified_in_db(original_webook_email)
                verification_process_successful = True # Set true because Webook account is verified
                final_retrieved_token = current_webook_access_token # Use old token as fallback
                logger.warning(last_error_message + " Using original token or will require manual token refresh.")
                break # Exit loop, primary goal (verification) achieved.

            # All steps succeeded for this pool_email_obj
            logger.info(f"All verification steps completed successfully using pool email: {pool_email_obj.email}")
            update_verification_email_stats_in_db(current_pool_email_id, success=True)
            final_retrieved_token = final_token_value
            verification_process_successful = True
            break  # --- EXIT THE POOL EMAIL LOOP ---

        except Exception as e:
            import traceback
            last_error_message = str(e)
            logger.error(f"Unhandled exception occurred with pool email {pool_email_obj.email}: {last_error_message}")
            logger.error(traceback.format_exc())
            if current_pool_email_id:
                 update_verification_email_stats_in_db(current_pool_email_id, success=False)
            continue # Try next pool email
        finally:
            if current_pool_email_id: # Ensure current_pool_email_id was set
                update_verification_email_status_in_db(current_pool_email_id, in_use=False)
    
    if not verification_process_successful:
        logger.error(f"Verification process failed for {original_webook_email}. Last error: {last_error_message}")
        return False, last_error_message
    
    # Steps 9 & 10: Update local DB with final token and mark as verified
    update_account_token_in_db(original_webook_email, final_retrieved_token) # Renamed
    mark_account_as_verified_in_db(original_webook_email) # Renamed
    
    logger.info(f"=== Verification completed successfully for: {original_webook_email} ===\n")
    return True, final_retrieved_token

# Renamed DB functions for clarity and consistency
def update_verification_email_status_in_db(email_id, in_use=None, is_burned=None):
    """Update verification email status (in_use, is_burned) in DB."""
    connection = None
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)
        with connection.cursor() as cursor:
            query_parts, params = [], []
            if in_use is not None:
                query_parts.append("in_use = %s")
                params.append(in_use)
            if is_burned is not None:
                query_parts.append("is_burned = %s")
                params.append(is_burned)
            if not query_parts: return False
            if in_use is False: query_parts.append("last_used = NOW()") # Update last_used when released
            
            query = f"UPDATE verification_emails SET {', '.join(query_parts)} WHERE id = %s"
            params.append(email_id)
            cursor.execute(query, tuple(params))
            connection.commit()
            logger.debug(f"Updated verification_emails status for id {email_id}: in_use={in_use}, is_burned={is_burned}")
            return True
    except pymysql.Error as e:
        logger.error(f"DB error updating verification_emails status for id {email_id}: {e}")
        return False
    finally:
        if connection: connection.close()

def update_verification_email_stats_in_db(email_id, success=False):
    """Update verification email statistics in DB."""
    connection = None
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)
        with connection.cursor() as cursor:
            query = """
            UPDATE verification_emails 
            SET verification_count = verification_count + 1,
                success_count = success_count + %s,
                failure_count = failure_count + %s
            WHERE id = %s
            """
            cursor.execute(query, (1 if success else 0, 0 if success else 1, email_id))
            connection.commit()
            logger.debug(f"Updated verification_emails stats for id {email_id}: success={success}")
            return True
    except pymysql.Error as e:
        logger.error(f"DB error updating verification_emails stats for id {email_id}: {e}")
        return False
    finally:
        if connection: connection.close()

def get_available_verification_emails_from_db(limit=5):
    """Get available verification emails from DB."""
    connection = None
    emails_list = [] # Renamed
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME, cursorclass=pymysql.cursors.DictCursor)
        with connection.cursor() as cursor:
            query = """
            SELECT * FROM verification_emails 
            WHERE is_active = TRUE AND in_use = FALSE AND is_burned = FALSE 
            ORDER BY last_used ASC, success_count DESC, verification_count ASC 
            LIMIT %s 
            """ # Prioritize emails that work well and haven't been used recently
            cursor.execute(query, (limit,))
            rows = cursor.fetchall()
            # Map to SimpleVerificationEmail objects for consistent attribute access
            emails_list = [SimpleVerificationEmail(**row) for row in rows]
            logger.info(f"Found {len(emails_list)} available verification emails from DB.")
    except pymysql.Error as e:
        logger.error(f"DB error getting verification_emails: {e}")
    finally:
        if connection: connection.close()
    return emails_list

def mark_account_as_verified_in_db(webook_email_address): # Renamed parameter
    """Mark a Webook account as verified in DB."""
    connection = None
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)
        with connection.cursor() as cursor:
            query = "UPDATE accounts SET is_verified = TRUE, updated_at = NOW() WHERE email = %s"
            cursor.execute(query, (webook_email_address,))
            connection.commit()
            if cursor.rowcount > 0:
                logger.info(f"Account {webook_email_address} marked as verified in local DB.")
                return True
            logger.warning(f"Account {webook_email_address} not found in local DB to mark as verified.")
            return False
    except pymysql.Error as e:
        logger.error(f"DB error marking {webook_email_address} as verified: {e}")
        return False
    finally:
        if connection: connection.close()

def update_account_token_in_db(webook_email_address, new_access_token): # Renamed
    """Update a Webook account's access token in DB."""
    connection = None
    try:
        connection = pymysql.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)
        with connection.cursor() as cursor:
            query = "UPDATE accounts SET access_token = %s, token_retrieved_at = NOW(), updated_at = NOW() WHERE email = %s"
            cursor.execute(query, (new_access_token, webook_email_address))
            connection.commit()
            if cursor.rowcount > 0:
                logger.info(f"Access token updated in local DB for {webook_email_address}.")
                return True
            logger.warning(f"Account {webook_email_address} not found in local DB to update token.")
            return False
    except pymysql.Error as e:
        logger.error(f"DB error updating token for {webook_email_address}: {e}")
        return False
    finally:
        if connection: connection.close()

async def check_account_verification_status(access_token_to_check): # Renamed parameter
    """Check Webook account verification status via API."""
    user_agent = await random_user_agent()
    headers = { 'authorization': f'Bearer {access_token_to_check}', 'user-agent': user_agent, 'token': SITE_TOKEN } # Simplified headers
    params = {'lang': 'en'}
    proxies = get_proxy_dict()
    
    logger.info(f"Checking Webook API for account verification status (token: ...{access_token_to_check[-10:] if access_token_to_check else 'N/A'}).")
    try:
        response = requests.get('https://api.webook.com/api/v2/user/profile', params=params, headers=headers, proxies=proxies, timeout=20)
        response.raise_for_status()
        response_data = response.json()
        if response_data.get('data', {}).get('email_verified') is not None:
            is_verified_api = bool(response_data['data']['email_verified']) # Renamed
            logger.info(f"Webook API status: {'Verified' if is_verified_api else 'Not Verified'}.")
            return is_verified_api, response_data
        else:
            logger.warning(f"Could not determine verification status from Webook API response: {response_data}")
            return False, "Verification status not found in API response"
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 401: # Unauthorized
            logger.warning(f"Webook API auth error (token may be invalid/expired): {e}")
            return False, "Authentication error with Webook API"
        logger.error(f"Webook API HTTPError checking status: {e}")
        return False, f"API HTTPError: {e}"
    except requests.exceptions.RequestException as e:
        logger.error(f"Webook API RequestException checking status: {e}")
        return False, f"API RequestException: {e}"
    except Exception as e:
        logger.error(f"Unexpected error checking Webook API status: {e}")
        return False, f"Unexpected API error: {e}"


# --- Helper functions for IMAP XOAUTH2 (specific to Hotmail/Outlook) ---
def get_access_token_hotmail(refresh_token, client_id): # No proxy needed for this specific token endpoint usually
    """Gets a new Microsoft OAuth access token from a refresh token."""
    try:
        post_data = {'client_id': client_id, 'refresh_token': refresh_token, 'grant_type': 'refresh_token'}
        response = requests.post("https://login.microsoftonline.com/common/oauth2/v2.0/token",
                                 data=post_data, headers={"Content-Type": "application/x-www-form-urlencoded"}, timeout=20)
        response.raise_for_status()
        response_data = response.json()
        access_token = response_data.get('access_token')
        if access_token:
            logger.info("Microsoft OAuth access token retrieved successfully.")
            return access_token
        else:
            logger.error(f"Failed to get Microsoft OAuth access token: {response_data.get('error_description', 'Unknown error')}")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException while getting Microsoft OAuth access token: {e}")
        return None
    except Exception as e: # Catch broader exceptions
        logger.error(f"Unexpected error getting Microsoft OAuth access token: {e}")
        return None


def generate_auth_string(user_email_for_auth, token_for_auth): # Renamed parameters
    """Formats the XOAUTH2 authentication string."""
    auth_str = f"user={user_email_for_auth}\1auth=Bearer {token_for_auth}\1\1" # Renamed
    return auth_str.encode('utf-8')

def main():
    """Main function to run the verification process from CLI."""
    parser = argparse.ArgumentParser(description='Webook Account Email Verification Script')
    parser.add_argument('--email', required=True, help='Webook account email to verify')
    parser.add_argument('--password', required=True, help='Webook account password')
    parser.add_argument('--token', required=True, help='Current Webook access token for the account')
    args = parser.parse_args()

    loop = asyncio.get_event_loop()
    success_status, result_message = loop.run_until_complete(
        verify_webook_account(args.email, args.password, args.token)) # Renamed

    if success_status:
        logger.info(f"Verification process completed successfully for {args.email}!")
        logger.info(f"New access token (or original if final login failed but verified): {result_message}")
    else:
        logger.error(f"Verification process failed for {args.email}: {result_message}")

if __name__ == "__main__":
    main()
