#!/usr/bin/env python3
"""
Direct MySQL Database Reset Script

This script directly connects to the MySQL database and drops all tables,
bypassing SQLAlchemy's constraints.

Usage:
    python direct_db_reset.py
"""

import pymysql
import getpass
import sys
import dotenv

dotenv.load_dotenv()

# Database connection parameters
DB_HOST = 'localhost'
DB_USER = 'webook_user'
DB_PASSWORD = 'webook_password'  # Change this or it will be prompted
DB_NAME = 'webook_db'

def get_all_tables(cursor):
    """Get all tables in the database."""
    cursor.execute("SHOW TABLES")
    return [table[0] for table in cursor.fetchall()]

def drop_all_tables():
    """Drop all tables in the database."""
    # Ask for password if not provided
    global DB_PASSWORD
    if DB_PASSWORD == 'webook_password':
        use_default = input(f"Use default password for {DB_USER}? (y/n): ").lower() == 'y'
        if not use_default:
            DB_PASSWORD = getpass.getpass(f"Enter password for {DB_USER}: ")
    
    try:
        # Connect to the database
        connection = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        with connection.cursor() as cursor:
            # Get all tables
            tables = get_all_tables(cursor)
            
            if not tables:
                print("No tables found in the database.")
                return True
            
            print(f"Found {len(tables)} tables to drop: {', '.join(tables)}")
            
            # Disable foreign key checks
            cursor.execute("SET FOREIGN_KEY_CHECKS=0")
            
            # Drop each table
            for table in tables:
                print(f"Dropping table: {table}")
                cursor.execute(f"DROP TABLE IF EXISTS `{table}`")
            
            # Re-enable foreign key checks
            cursor.execute("SET FOREIGN_KEY_CHECKS=1")
            
            # Commit the changes
            connection.commit()
            
            print("All tables dropped successfully.")
            return True
            
    except pymysql.Error as e:
        print(f"Database error: {str(e)}")
        return False
    finally:
        if 'connection' in locals() and connection:
            connection.close()

def main():
    """Main function."""
    print("======== Direct MySQL Database Reset ========")
    print("\nWARNING: This will delete ALL tables in the database!")
    print(f"Database: {DB_NAME} on {DB_HOST}")
    print("Make sure to back up any important data before proceeding.")
    
    confirmation = input("\nType 'RESET' to confirm: ")
    
    if confirmation != "RESET":
        print("Reset cancelled.")
        return
    
    print("\nResetting database...")
    
    if drop_all_tables():
        print("\n======== Database Reset Complete ========")
        print("\nYou can now initialize your application tables with:")
        print("  flask init-db")
    else:
        print("\nDatabase reset failed.")

if __name__ == "__main__":
    main()