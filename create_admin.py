#!/usr/bin/env python3
"""
Database Check and Admin User Creation Script

This script connects directly to the MySQL database, checks table existence,
and creates an admin user if needed.

Usage:
    python check_and_create_admin.py
"""

import pymysql
import getpass
import sys
from werkzeug.security import generate_password_hash

# Database connection parameters
DB_HOST = 'localhost'
DB_USER = 'webook_user'
DB_PASSWORD = 'webook_password'  # Change this or it will be prompted
DB_NAME = 'webook_db'

def check_database():
    """Check database connection and tables."""
    # Ask for password if not provided
    global DB_PASSWORD
    if DB_PASSWORD == 'webook_password':
        use_default = input(f"Use default password for {DB_USER}? (y/n): ").lower() == 'y'
        if not use_default:
            DB_PASSWORD = getpass.getpass(f"Enter password for {DB_USER}: ")
    
    try:
        # Connect to the database
        connection = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        print(f"Successfully connected to {DB_NAME} database.")
        
        with connection.cursor() as cursor:
            # Check tables
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            print(f"Found {len(tables)} tables in the database:")
            for table in tables:
                print(f"  - {table}")
            
            # Check users table
            if 'users' in tables:
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                print(f"Found {user_count} users in the users table.")
                
                # Check admin users
                cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
                admin_count = cursor.fetchone()[0]
                print(f"Found {admin_count} admin users.")
                
                if admin_count == 0:
                    print("No admin users found. You may want to create one.")
            else:
                print("Users table not found!")
            
            return connection
            
    except pymysql.Error as e:
        print(f"Database error: {str(e)}")
        return None

def create_admin_user(connection):
    """Create an admin user in the database."""
    print("\n--- Create Admin User ---")
    
    username = input("Admin username: ") or "admin"
    password = getpass.getpass("Admin password: ") or "changeme"
    
    # Generate password hash
    password_hash = generate_password_hash(password)
    
    try:
        with connection.cursor() as cursor:
            # First check if the users table exists
            cursor.execute("SHOW TABLES LIKE 'users'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print("Creating users table...")
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS `users` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `username` VARCHAR(100) UNIQUE NOT NULL,
                    `password_hash` VARCHAR(255) NOT NULL,
                    `is_admin` BOOLEAN DEFAULT FALSE,
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """)
            
            # Check if user already exists
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            existing_user = cursor.fetchone()
            
            if existing_user:
                update = input(f"User '{username}' already exists. Update password? (y/n): ").lower() == 'y'
                if update:
                    cursor.execute(
                        "UPDATE users SET password_hash = %s WHERE username = %s",
                        (password_hash, username)
                    )
                    print(f"Password updated for user '{username}'.")
                else:
                    print("Password not updated.")
            else:
                # Insert admin user
                cursor.execute(
                    "INSERT INTO users (username, password_hash, is_admin) VALUES (%s, %s, %s)",
                    (username, password_hash, True)
                )
                print(f"Admin user '{username}' created successfully.")
            
            connection.commit()
            return True
            
    except pymysql.Error as e:
        print(f"Error creating admin user: {str(e)}")
        return False

def main():
    """Main function."""
    print("======== Database Check and Admin User Creation ========\n")
    
    # Check database
    connection = check_database()
    
    if not connection:
        print("Failed to connect to the database.")
        return
    
    try:
        # Ask if the user wants to create an admin user
        create_admin = input("\nDo you want to create an admin user? (y/n): ").lower() == 'y'
        
        if create_admin:
            if create_admin_user(connection):
                print("\nAdmin user created successfully.")
            else:
                print("\nFailed to create admin user.")
        
        print("\n======== Process Completed ========")
        
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    main()