{% extends 'base.html' %}

{% block title %}Verify & Get Tokens - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Verify Accounts & Get Tokens</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('dashboard') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Account Verification & Token Management</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Prioritizing Verification</h6>
                    <hr>
                    <p class="mb-0">Verified accounts are more valuable. This tool now prioritizes verification first, then token retrieval.</p>
                </div>
                
                <form method="post">
                    <div class="mb-3">
                        <label for="count" class="form-label">Number of Accounts to Process</label>
                        <input type="number" class="form-control" id="count" name="count" min="-1" max="100" value="10" required>
                        <div class="form-text">
                            Enter how many accounts you want to process. Use -1 to process all unverified accounts.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="proxy" class="form-label">Proxy (Optional)</label>
                        <input type="text" class="form-control" id="proxy" name="proxy" placeholder="user:pass@ip:port">
                        <div class="form-text">
                            If you want to use a proxy for the requests, enter it here. Format: <code>user:pass@ip:port</code> or <code>ip:port</code>
                        </div>
                    </div>
                    
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">Processing Mode</h6>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="verify_first" name="verify_first" checked>
                                <label class="form-check-label" for="verify_first">
                                    <strong>Verify First Mode (Recommended)</strong>
                                    <br>
                                    <small class="text-muted">Process unverified accounts: Get token → Verify email → Update token</small>
                                </label>
                            </div>
                            <div class="form-text">
                                When enabled, the system will:
                                <ol class="mb-0">
                                    <li>Find unverified accounts</li>
                                    <li>Get a token if needed</li>
                                    <li>Verify the account email</li>
                                    <li>Update with fresh verified token</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning" id="tokenOnlyWarning" style="display: none;">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Token-Only Mode</h6>
                        <p class="mb-0">This mode only retrieves tokens without verification. Unverified accounts have limited value.</p>
                    </div>
                    
                    <div class="mb-0">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-play-circle me-1"></i> Start Processing
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4 border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Account Status Overview</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <div>Total Accounts:</div>
                    <div class="fw-bold">{{ stats.total_accounts }}</div>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-2 text-danger">
                    <div>Unverified Accounts:</div>
                    <div class="fw-bold">{{ stats.unverified_accounts }}</div>
                </div>
                <div class="d-flex justify-content-between mb-2 text-success">
                    <div>Verified Accounts:</div>
                    <div class="fw-bold">{{ stats.verified_accounts }}</div>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <div>Verification Rate:</div>
                    <div class="fw-bold">{{ stats.verification_success_rate }}%</div>
                </div>
                
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ stats.verification_success_rate }}%;" 
                         aria-valuenow="{{ stats.verification_success_rate }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">Verified {{ stats.verification_success_rate }}%</div>
                    <div class="progress-bar bg-warning" role="progressbar" 
                         style="width: {{ 100 - stats.verification_success_rate }}%;" 
                         aria-valuenow="{{ 100 - stats.verification_success_rate }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">Unverified {{ 100 - stats.verification_success_rate }}%</div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="{{ url_for('verification_emails') }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i> Manage Verification Emails
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Quick Stats</h5>
            </div>
            <div class="card-body">
                <h6>Verification Emails</h6>
                <div class="d-flex justify-content-between mb-2">
                    <div>Active:</div>
                    <div class="fw-bold text-success">{{ stats.verification_emails.active }}</div>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <div>Available:</div>
                    <div class="fw-bold">{{ stats.verification_emails.active - stats.waiting_for_verification if stats.verification_emails.active > stats.waiting_for_verification else 0 }}</div>
                </div>
                
                <hr>
                
                <h6>Token Status</h6>
                <div class="d-flex justify-content-between mb-2">
                    <div>Valid Tokens:</div>
                    <div class="fw-bold">{{ stats.valid_tokens }}</div>
                </div>
                <div class="d-flex justify-content-between">
                    <div>Verified + Valid:</div>
                    <div class="fw-bold text-success">{{ stats.verified_with_valid_tokens }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const verifyFirstCheckbox = document.getElementById('verify_first');
        const tokenOnlyWarning = document.getElementById('tokenOnlyWarning');
        
        // Show/hide warning based on checkbox
        verifyFirstCheckbox.addEventListener('change', function() {
            tokenOnlyWarning.style.display = this.checked ? 'none' : 'block';
        });
    });
</script>
{% endblock %}