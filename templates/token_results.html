{% extends 'base.html' %}

{% block title %}Token Results - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Token Retrieval Results</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('get_tokens') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Get Tokens
            </a>
            <button class="btn btn-sm btn-outline-primary" id="exportResultsBtn">
                <i class="fas fa-download me-1"></i> Export Results
            </button>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Results Summary</h5>
            <span class="badge bg-primary">{{ results|length }} Accounts Processed</span>
        </div>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card border-success mb-3">
                    <div class="card-body">
                        <h1 class="display-4">{{ results|selectattr('status', 'equalto', 'success')|list|length }}</h1>
                        <p class="text-success">Successful</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-danger mb-3">
                    <div class="card-body">
                        <h1 class="display-4">{{ results|selectattr('status', 'equalto', 'error')|list|length }}</h1>
                        <p class="text-danger">Failed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-info mb-3">
                    <div class="card-body">
                        <h1 class="display-4">{{ (results|selectattr('status', 'equalto', 'success')|list|length / results|length * 100)|round|int }}%</h1>
                        <p class="text-info">Success Rate</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Detailed Results</h5>
            <div>
                <button class="btn btn-sm btn-outline-success" onclick="filterResults('success')">Show Successful</button>
                <button class="btn btn-sm btn-outline-danger" onclick="filterResults('error')">Show Failed</button>
                <button class="btn btn-sm btn-outline-secondary" onclick="filterResults('all')">Show All</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="resultsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Token / Error</th>
                        <th>Verification</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in results %}
                    <tr class="result-row {{ result.status }}">
                        <td>{{ loop.index }}</td>
                        <td>{{ result.email }}</td>
                        <td>
                            {% if result.status == 'success' %}
                            <span class="badge bg-success">Success</span>
                            {% else %}
                            <span class="badge bg-danger">Error</span>
                            {% endif %}
                        </td>
                        <td class="text-truncate" style="max-width: 350px;">
                            {% if result.status == 'success' %}
                            <div class="text-success">Token retrieved successfully</div>
                            {% else %}
                            <div class="text-danger">{{ result.error }}</div>
                            {% endif %}
                        </td>
                        <td>
                            {% if result.verification.attempted %}
                                {% if result.verification.success %}
                                <span class="badge bg-success">Verified</span>
                                {% else %}
                                <span class="badge bg-warning text-dark">Failed</span>
                                <small class="d-block text-truncate" style="max-width: 150px;">{{ result.verification.result }}</small>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">Not Attempted</span>
                            {% endif %}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary view-details-btn" data-index="{{ loop.index0 }}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>            </table>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">Account Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Email:</label>
                    <input type="text" class="form-control" id="modalEmail" readonly>
                </div>
                <div id="successContent">
                    <div class="alert alert-success mb-3">
                        <h5>Token retrieved successfully!</h5>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Access Token:</label>
                        <div class="input-group">
                            <textarea class="form-control token-area" id="modalToken" rows="6" readonly></textarea>
                            <button class="btn btn-outline-secondary" type="button" id="copyModalTokenBtn">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="errorContent">
                    <div class="alert alert-danger mb-3">
                        <h5>Error retrieving token</h5>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Error Message:</label>
                        <textarea class="form-control" id="modalError" rows="3" readonly></textarea>
                    </div>
                </div>
                <div id="verificationContent">
                    <h6 class="border-bottom pb-2 mb-3">Verification Results</h6>
                    <div class="mb-3" id="verificationAttempted">
                        <div class="alert" id="verificationAlert">
                            <h6 id="verificationStatus"></h6>
                            <p class="mb-0" id="verificationMessage"></p>
                        </div>
                    </div>
                    <div class="mb-3" id="verificationNotAttempted">
                        <div class="alert alert-secondary">
                            <h6>Verification Not Attempted</h6>
                            <p class="mb-0">Email verification was not attempted for this account.</p>
                            <div class="mt-2">
                                <a href="#" class="btn btn-sm btn-outline-primary" id="verifyNowBtn">Verify Now</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update the view details function
    viewDetailsBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            const result = results[index];
            
            modalEmail.value = result.email;
            
            // Handle token/error display
            if (result.status === 'success') {
                successContent.style.display = 'block';
                errorContent.style.display = 'none';
                modalToken.value = result.token;
            } else {
                successContent.style.display = 'none';
                errorContent.style.display = 'block';
                modalError.value = result.error;
            }
            
            // Handle verification display
            if (result.verification && result.verification.attempted) {
                verificationAttempted.style.display = 'block';
                verificationNotAttempted.style.display = 'none';
                
                if (result.verification.success) {
                    verificationAlert.className = 'alert alert-success';
                    verificationStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i> Verification Successful';
                    verificationMessage.textContent = 'The account email was successfully verified.';
                } else {
                    verificationAlert.className = 'alert alert-warning';
                    verificationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Verification Failed';
                    verificationMessage.textContent = result.verification.result || 'An error occurred during verification.';
                }
            } else {
                verificationAttempted.style.display = 'none';
                verificationNotAttempted.style.display = 'block';
                
                // Set up the Verify Now button
                document.getElementById('verifyNowBtn').setAttribute('data-email', result.email);
                document.getElementById('verifyNowBtn').setAttribute('data-token', result.token || '');
            }
            
            detailsModal.show();
        });
    });
    
    // Verify Now button
    document.getElementById('verifyNowBtn').addEventListener('click', function(e) {
        e.preventDefault();
        
        const email = this.getAttribute('data-email');
        const token = this.getAttribute('data-token');
        
        if (!token) {
            alert('No token available for verification. Please retrieve a token first.');
            return;
        }
        
        if (confirm(`Are you sure you want to verify the email for ${email}?`)) {
            // Redirect to the verification page
            window.location.href = `/email/verify-emails?email=${encodeURIComponent(email)}&token=${encodeURIComponent(token)}`;
        }
    });
</script>
{% endblock %}