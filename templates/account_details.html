{% extends 'base.html' %}

{% block title %}Account Details - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Account Details</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('accounts') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Accounts
            </a>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Account Information</h5>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Email:</div>
            <div class="col-md-9">{{ account.email }}</div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Password:</div>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="password" class="form-control" id="passwordField" value="{{ account.password }}" readonly>
                    <button class="btn btn-outline-secondary" type="button" id="togglePasswordBtn">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-secondary" type="button" id="copyPasswordBtn">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Status:</div>
            <div class="col-md-9">
                {% if account.is_dead %}
                <span class="badge bg-danger">Dead</span>
                {% else %}
                <span class="badge bg-success">Active</span>
                {% endif %}
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Verified:</div>
            <div class="col-md-9">
                {% if account.is_verified %}
                <span class="badge bg-success">Verified</span>
                {% else %}
                <span class="badge bg-warning text-dark">Not Verified</span>
                {% endif %}
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Created:</div>
            <div class="col-md-9">{{ account.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
        </div>
        <div class="row mb-3">
            <div class="col-md-3 fw-bold">Last Updated:</div>
            <div class="col-md-9">{{ account.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Access Token</h5>
    </div>
    <div class="card-body">
        {% if account.access_token %}
        <div class="mb-3">
            <div class="input-group">
                <textarea class="form-control" id="tokenField" rows="4" readonly>{{ account.access_token }}</textarea>
                <button class="btn btn-outline-secondary" type="button" id="copyTokenBtn">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
        <div class="mb-3">
            <strong>Token Retrieved:</strong> 
            {% if account.token_retrieved_at %}
            {{ account.token_retrieved_at.strftime('%Y-%m-%d %H:%M:%S') }}
            {% else %}
            Never
            {% endif %}
        </div>
        <div class="mb-3">
            <strong>Token Status:</strong>
            {% if account.is_token_valid() %}
            <span class="badge bg-success">Valid</span>
            {% else %}
            <span class="badge bg-warning text-dark">Expired or Invalid</span>
            {% endif %}
        </div>
        {% else %}
        <div class="alert alert-info mb-0">
            <i class="fas fa-info-circle me-2"></i> No access token available for this account. Use the "Refresh Token" button to retrieve one.
        </div>
        {% endif %}
    </div>
    <div class="card-footer">
        <button type="button" class="btn btn-primary" id="refreshTokenBtn">
            <i class="fas fa-sync-alt me-1"></i> Refresh Token
        </button>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Account Actions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <button type="button" class="btn btn-success w-100" id="testAccountBtn">
                    <i class="fas fa-check-circle me-1"></i> Test Account
                </button>
            </div>
            <div class="col-md-4 mb-3">
                <button type="button" class="btn btn-{{ 'success' if account.is_dead else 'danger' }} w-100" id="toggleDeadBtn">
                    <i class="fas fa-{{ 'undo' if account.is_dead else 'ban' }} me-1"></i> 
                    {{ 'Mark as Active' if account.is_dead else 'Mark as Dead' }}
                </button>
            </div>
            <div class="col-md-4 mb-3">
                <button type="button" class="btn btn-{{ 'danger' if account.is_verified else 'warning' }} w-100" id="toggleVerifiedBtn">
                    <i class="fas fa-{{ 'times' if account.is_verified else 'check' }}-circle me-1"></i>
                    {{ 'Unverify' if account.is_verified else 'Verify' }}
                </button>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <button type="button" class="btn btn-danger" id="deleteAccountBtn">
            <i class="fas fa-trash me-1"></i> Delete Account
        </button>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="loadingModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 id="loadingMessage">Processing...</h5>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Delete Modal -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this account?</p>
                <p><strong>Email:</strong> {{ account.email }}</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password toggle and copy
        const passwordField = document.getElementById('passwordField');
        const togglePasswordBtn = document.getElementById('togglePasswordBtn');
        const copyPasswordBtn = document.getElementById('copyPasswordBtn');
        
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
        });
        
        copyPasswordBtn.addEventListener('click', function() {
            passwordField.setAttribute('type', 'text');
            passwordField.select();
            document.execCommand('copy');
            passwordField.setAttribute('type', 'password');
            togglePasswordBtn.innerHTML = '<i class="fas fa-eye"></i>';
            
            // Show copy feedback
            const originalHTML = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                this.innerHTML = originalHTML;
            }, 2000);
        });
        
        // Token copy
        const tokenField = document.getElementById('tokenField');
        const copyTokenBtn = document.getElementById('copyTokenBtn');
        
        if (copyTokenBtn) {
            copyTokenBtn.addEventListener('click', function() {
                tokenField.select();
                document.execCommand('copy');
                
                // Show copy feedback
                const originalHTML = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                }, 2000);
            });
        }
        
        // Refresh token button
        const refreshTokenBtn = document.getElementById('refreshTokenBtn');
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        const loadingMessage = document.getElementById('loadingMessage');
        
        refreshTokenBtn.addEventListener('click', function() {
            loadingMessage.textContent = 'Retrieving token...';
            loadingModal.show();
            
            // Make API request to login 
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: '{{ account.email }}',
                    password: '{{ account.password }}'
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingModal.hide();
                
                if (data.status === 'success') {
                    // Reload page to show updated token
                    window.location.reload();
                } else {
                    alert('Error retrieving token: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                loadingModal.hide();
                alert('Network error: ' + error.message);
            });
        });
        
        // Toggle dead status
        const toggleDeadBtn = document.getElementById('toggleDeadBtn');
        
        toggleDeadBtn.addEventListener('click', function() {
            const isCurrentlyDead = {{ 'true' if account.is_dead else 'false' }};
            const newStatus = !isCurrentlyDead;
            
            if (confirm(`Are you sure you want to mark this account as ${newStatus ? 'dead' : 'active'}?`)) {
                // Make API request to update status
                fetch(`/api/account/{{ account.id }}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        is_dead: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Reload page to show updated status
                        window.location.reload();
                    } else {
                        alert('Error updating status: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Network error: ' + error.message);
                });
            }
        });

        // Toggle verified status
        const toggleVerifiedBtn = document.getElementById('toggleVerifiedBtn');
        
        toggleVerifiedBtn.addEventListener('click', function() {
            const isCurrentlyVerified = {{ 'true' if account.is_verified else 'false' }};
            const newStatus = !isCurrentlyVerified;
            const endpoint = newStatus ? 
                `/api/account/{{ account.id }}/verify` : 
                `/api/account/{{ account.id }}/unverify`;
            
            if (confirm(`Are you sure you want to mark this account as ${newStatus ? 'verified' : 'not verified'}?`)) {
                // Make API request to update verification status
                loadingMessage.textContent = `Updating verification status...`;
                loadingModal.show();
                
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    loadingModal.hide();
                    
                    if (data.status === 'success') {
                        // Reload page to show updated status
                        window.location.reload();
                    } else {
                        alert('Error updating verification status: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    loadingModal.hide();
                    alert('Network error: ' + error.message);
                });
            }
        });
        
        // Delete account
        const deleteAccountBtn = document.getElementById('deleteAccountBtn');
        const confirmDeleteModal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        
        deleteAccountBtn.addEventListener('click', function() {
            confirmDeleteModal.show();
        });
        
        confirmDeleteBtn.addEventListener('click', function() {
            // Make API request to delete the account
            confirmDeleteModal.hide();
            loadingMessage.textContent = 'Deleting account...';
            loadingModal.show();
            
            fetch(`/api/account/{{ account.email }}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                loadingModal.hide();
                
                if (data.status === 'success') {
                    // Redirect to accounts page
                    window.location.href = '{{ url_for("accounts") }}';
                } else {
                    alert('Error deleting account: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                loadingModal.hide();
                alert('Network error: ' + error.message);
            });
        });
        
        // Test account
        const testAccountBtn = document.getElementById('testAccountBtn');
        
        testAccountBtn.addEventListener('click', function() {
            loadingMessage.textContent = 'Testing account...';
            loadingModal.show();
            
            // Make API request to test the account (use login API)
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: '{{ account.email }}',
                    password: '{{ account.password }}'
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingModal.hide();
                
                if (data.status === 'success') {
                    alert('Account test successful! Valid credentials.');
                    // Reload page to show updated token
                    window.location.reload();
                } else {
                    alert('Account test failed: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                loadingModal.hide();
                alert('Network error: ' + error.message);
            });
        });
    });
</script>
{% endblock %}