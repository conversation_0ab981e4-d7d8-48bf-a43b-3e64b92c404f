<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Webook Admin{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: sticky;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
        }
        
        .navbar .navbar-toggler {
            top: .25rem;
            right: 1rem;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
        }
        
        .sidebar .nav-link.active {
            color: #007bff;
        }
        
        main {
            padding-top: 1.5rem;
        }
        
        .token-area {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="{{ url_for('dashboard') }}">Webook Admin</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100"></div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                {% if current_user.is_authenticated %}
                <a class="nav-link px-3" href="{{ url_for('logout') }}">Sign out</a>
                {% else %}
                <a class="nav-link px-3" href="{{ url_for('login') }}">Sign in</a>
                {% endif %}
            </div>
        </div>
    </header>
    
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('dashboard') else '' }}" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('accounts') else '' }}" href="{{ url_for('accounts') }}">
                                <i class="fas fa-users me-2"></i>
                                Accounts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('get_tokens') else '' }}" href="{{ url_for('get_tokens') }}">
                                <i class="fas fa-key me-2"></i>
                                Get Tokens
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('email_verifier.verify_emails_dashboard') else '' }}" 
                               href="{{ url_for('email_verifier.verify_emails_dashboard') }}">
                                <i class="fas fa-envelope-open-text me-2"></i>
                                Email Verification
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('verification_emails') else '' }}" 
                               href="{{ url_for('verification_emails') }}">
                                <i class="fas fa-envelope me-2"></i>
                                Verification Emails
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('upload_accounts') else '' }}" href="{{ url_for('upload_accounts') }}">
                                <i class="fas fa-upload me-2"></i>
                                Upload Accounts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.path == url_for('settings') else '' }}" href="{{ url_for('settings') }}">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>API Documentation</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#apiDocsModal">
                                <i class="fas fa-file-code me-2"></i>
                                API Endpoints
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            {% endif %}
            
            <main class="{% if current_user.is_authenticated %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-md-12{% endif %}">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- API Docs Modal -->
    <div class="modal fade" id="apiDocsModal" tabindex="-1" aria-labelledby="apiDocsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="apiDocsModalLabel">API Documentation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h5>Endpoints</h5>
                    <div class="list-group mb-3">
                        <div class="list-group-item">
                            <h6>POST /api/login</h6>
                            <p class="mb-1">Login with Webook credentials and get an access token.</p>
                            <small class="text-muted">
                                <pre>{
    "email": "<EMAIL>",
    "password": "password123",
    "proxy": "optional_proxy"
}</pre>
                            </small>
                        </div>
                        <div class="list-group-item">
                            <h6>GET /api/token/:email</h6>
                            <p class="mb-1">Get stored token for a specific email.</p>
                        </div>
                        <div class="list-group-item">
                            <h6>GET /api/token</h6>
                            <p class="mb-1">Get a rotating token from the valid token pool.</p>
                        </div>
                        <div class="list-group-item">
                            <h6>GET /api/stats</h6>
                            <p class="mb-1">Get account statistics (total, available, dead, valid tokens).</p>
                        </div>
                        <div class="list-group-item">
                            <h6>POST /api/upload-accounts</h6>
                            <p class="mb-1">Bulk upload accounts.</p>
                            <small class="text-muted">
                                <pre>{
    "accounts": [
        {"email": "<EMAIL>", "password": "pass1"},
        {"email": "<EMAIL>", "password": "pass2"}
    ]
}</pre>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>