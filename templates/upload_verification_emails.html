{% extends 'base.html' %}

{% block title %}Upload Verification Emails - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Upload Verification Emails</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('verification_emails') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Verification Emails
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">Upload Email List</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="file" class="form-label">Select File</label>
                        <input class="form-control" type="file" id="file" name="file" required>
                        <div class="form-text">
                            Upload a text file containing verification email information, one per line.
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>File Format Requirements</h6>
                        <hr>
                        <p class="mb-1">Each line should use a pipe `|` as a separator.</p>
                        <p class="mb-1"><strong>Format:</strong> <code>email|password|refresh_token|client_id</code></p>
                        <p class="mb-0"><code>password</code> can be left empty if using OAuth2, but the `|` separators must still be present.</p>
                        <p class="mb-0 mt-2">Example: <code><EMAIL>||long_refresh_token_here|client_id_here</code></p>
                    </div>
                    
                    <div class="mb-0">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i> Upload Verification Emails
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}