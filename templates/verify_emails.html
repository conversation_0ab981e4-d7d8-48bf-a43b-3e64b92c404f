{% extends 'base.html' %}

{% block title %}Email Verification - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Email Verification</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('dashboard') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Verify Single Account</h5>
            </div>
            <div class="card-body">
                <form id="singleVerificationForm">
                    <div class="mb-3">
                        <label for="account_id" class="form-label">Select Account</label>
                        <select class="form-select" id="account_id" name="account_id" required>
                            <option value="">-- Select an account --</option>
                            {% for account in accounts %}
                            <option value="{{ account.id }}" data-email="{{ account.email }}" data-password="{{ account.password }}" data-token="{{ account.access_token }}">
                                {{ account.email }} 
                                {% if account.is_verified %}<span class="text-success">(Verified)</span>
                                {% elif account.is_dead %}<span class="text-danger">(Dead)</span>
                                {% else %}<span class="text-warning">(Unverified)</span>
                                {% endif %}
                                {% if account.access_token and account.is_token_valid() %}(Valid Token)
                                {% elif account.access_token %} (Expired Token)
                                {% else %} (No Token)
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="togglePasswordBtn">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <div>
                                <i class="fas fa-info-circle fa-lg me-2"></i>
                            </div>
                            <div>
                                <p class="mb-0">This process will use a verification email from your pool to:</p>
                                <ol class="mb-0">
                                    <li>Temporarily change the Webook account's email.</li>
                                    <li>Retrieve the verification code from the pool email.</li>
                                    <li>Submit the code to verify the Webook account.</li>
                                    <li>Revert the Webook account's email to original.</li>
                                    <li>Update the account with a fresh token.</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <button type="submit" class="btn btn-primary" id="verifyBtn">
                            <i class="fas fa-check-circle me-1"></i> Verify Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Bulk Verification</h5>
            </div>
            <div class="card-body">
                <p>Verify multiple unverified accounts at once:</p>
                
                <form id="bulkVerificationForm">
                    <div class="mb-3">
                        <label for="bulk_count" class="form-label">Number of Accounts to Verify</label>
                        <input type="number" class="form-control" id="bulk_count" name="bulk_count" min="1" max="100" value="10">
                        <div class="form-text">
                            Select how many unverified, non-dead accounts to process. The system will get tokens if needed.
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Note:</strong> This will process unverified, non-dead accounts only. If an account doesn't have a token, one will be retrieved first.
                    </div>
                    
                    <div class="mb-0">
                        <button type="submit" class="btn btn-primary" id="bulkVerifyBtn">
                            <i class="fas fa-tasks me-1"></i> Start Bulk Verification
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Verification Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h3 class="text-primary">{{ verification_stats.total }}</h3>
                        <p class="text-muted mb-0">Total Accounts</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-success">{{ verification_stats.verified }}</h3>
                        <p class="text-muted mb-0">Verified</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-warning">{{ verification_stats.unverified }}</h3>
                        <p class="text-muted mb-0">Unverified (Active)</p>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ verification_stats.verification_rate }}%;" 
                         aria-valuenow="{{ verification_stats.verification_rate }}" 
                         aria-valuemin="0" aria-valuemax="100">
                        {{ verification_stats.verification_rate }}% Verified
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">Verification in Progress</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="initialSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2" id="progressMessage">Initializing verification process...</p>
                </div>
                
                <div id="progressDetails" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    
                    <h6>Account: <span id="currentAccount">-</span></h6>
                    <div id="stepsList" class="list-group mb-3" style="max-height: 300px; overflow-y: auto;">
                        </div>
                </div>
                
                <div id="bulkResultsContainer" style="display: none;">
                    <h6 class="border-bottom pb-2 mb-3">Results Summary</h6>
                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Message</th>
                                </tr>
                            </thead>
                            <tbody id="bulkResults">
                                </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeProgressBtn" disabled>Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Account selection for single verification
        const accountSelect = document.getElementById('account_id');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        
        if (accountSelect) {
            accountSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                emailInput.value = selectedOption.getAttribute('data-email') || '';
                passwordInput.value = selectedOption.getAttribute('data-password') || '';
            });
        }
        
        // Password toggle for single verification
        const togglePasswordBtn = document.getElementById('togglePasswordBtn');
        if (togglePasswordBtn) {
            togglePasswordBtn.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
            });
        }
        
        // Modal elements
        const progressModalElement = document.getElementById('progressModal');
        const progressModal = new bootstrap.Modal(progressModalElement);
        const progressMessage = document.getElementById('progressMessage');
        const progressBar = document.getElementById('progressBar');
        const initialSpinner = document.getElementById('initialSpinner');
        const progressDetails = document.getElementById('progressDetails');
        const bulkResultsContainer = document.getElementById('bulkResultsContainer');
        const bulkResultsTableBody = document.getElementById('bulkResults'); // Corrected ID
        const currentAccountSpan = document.getElementById('currentAccount'); // Corrected ID
        const stepsList = document.getElementById('stepsList');
        const closeProgressBtn = document.getElementById('closeProgressBtn');
        
        // Single account verification form submission
        const singleVerificationForm = document.getElementById('singleVerificationForm');
        if (singleVerificationForm) {
            singleVerificationForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!confirm('Are you sure you want to verify this account? This will use one of your verification emails.')) {
                    return;
                }
                
                const selectedOption = accountSelect.options[accountSelect.selectedIndex];
                const email = emailInput.value;
                const password = passwordInput.value;
                const accessToken = selectedOption.getAttribute('data-token');
                
                if (!email || !password ) { // Access token might not be present initially for an unverified account
                    alert('Email and password are required. An access token will be fetched if not present.');
                    // We will rely on the backend to fetch token if needed.
                }
                 if (!accessToken && email && password) {
                    console.warn("No access token found for single verification, backend will attempt to fetch one.");
                }


                // Reset modal state
                progressBar.style.width = '0%';
                progressBar.textContent = '0%';
                progressBar.setAttribute('aria-valuenow', 0);
                initialSpinner.style.display = 'block';
                progressDetails.style.display = 'none';
                bulkResultsContainer.style.display = 'none';
                closeProgressBtn.disabled = true;
                progressMessage.textContent = 'Verifying email...';
                stepsList.innerHTML = '';
                currentAccountSpan.textContent = email;
                
                progressModal.show();
                
                // Add initial step
                progressDetails.style.display = 'block';
                initialSpinner.style.display = 'none';
                addStep('Starting verification process for ' + email, 'primary', true);
                
                // Make API call to verify the email (using the endpoint from email_verifier.py)
                fetch('/email/verify-emails', { // This is the single verification endpoint
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        access_token: accessToken // Send current token, backend handles refresh if needed
                    })
                })
                .then(response => response.json())
                .then(data => {
                    updateProgress(100);
                    if (data.status === 'success') {
                        addStep('Email verified successfully. New token: ' + data.new_access_token.substring(0,20) + "...", 'success');
                        alert('Verification successful!');
                        window.location.reload(); // Reload to reflect changes
                    } else {
                        addStep('Verification failed: ' + data.error, 'danger');
                        alert('Verification failed: ' + data.error);
                    }
                    closeProgressBtn.disabled = false;
                })
                .catch(error => {
                    updateProgress(100);
                    addStep('Network error during verification: ' + error.message, 'danger');
                    alert('Network error: ' + error.message);
                    closeProgressBtn.disabled = false;
                });
            });
        }

        // Bulk verification form submission
        const bulkVerificationForm = document.getElementById('bulkVerificationForm');
        if (bulkVerificationForm) {
            bulkVerificationForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const count = parseInt(document.getElementById('bulk_count').value);
                if (isNaN(count) || count < 1) {
                    alert('Please enter a valid number of accounts to verify.');
                    return;
                }
                
                if (!confirm(`Are you sure you want to attempt to verify ${count} accounts? This may take some time.`)) {
                    return;
                }
                
                // Reset modal state for bulk
                resetModalForBulk(`Preparing to verify ${count} accounts...`);
                progressModal.show();

                try {
                    // Step 1: Fetch unverified accounts from the backend
                    addStep('Fetching unverified accounts...', 'primary', true);
                    const response = await fetch(`/api/accounts?status=unverified&limit=${count}`);
                    if (!response.ok) throw new Error(`Failed to fetch accounts: ${response.statusText}`);
                    const accountData = await response.json();
                    const accountsToProcess = accountData.accounts;

                    addStep(`Found ${accountsToProcess.length} unverified accounts to process.`, 'info');

                    if (accountsToProcess.length === 0) {
                        addStep('No unverified accounts found to process.', 'warning');
                        closeProgressBtn.disabled = false;
                        updateProgress(100);
                        return;
                    }
                    
                    progressMessage.textContent = `Processing ${accountsToProcess.length} unverified accounts...`;
                    initialSpinner.style.display = 'none';
                    progressDetails.style.display = 'block';
                    bulkResultsContainer.style.display = 'block'; // Show table container
                    bulkResultsTableBody.innerHTML = ''; // Clear previous results

                    // Step 2: Process each account sequentially
                    const overallResults = [];
                    for (let i = 0; i < accountsToProcess.length; i++) {
                        const account = accountsToProcess[i];
                        currentAccountSpan.textContent = account.email;
                        updateProgress(Math.round(((i + 1) / accountsToProcess.length) * 100));
                        stepsList.innerHTML = ''; // Clear steps for new account

                        let currentAccessToken = account.access_token;

                        // Sub-step 2a: Get token if missing or potentially invalid (backend should handle this robustly)
                        if (!currentAccessToken) {
                            addStep(`Fetching token for ${account.email}...`, 'primary', true);
                            try {
                                const tokenResponse = await fetch('/api/login', {
                                    method: 'POST',
                                    headers: {'Content-Type': 'application/json'},
                                    body: JSON.stringify({email: account.email, password: account.password})
                                });
                                const tokenResult = await tokenResponse.json();
                                if (tokenResult.status === 'success') {
                                    currentAccessToken = tokenResult.access_token;
                                    addStep(`Token fetched for ${account.email}.`, 'success');
                                } else {
                                    addStep(`Failed to fetch token for ${account.email}: ${tokenResult.error}`, 'danger');
                                    addBulkResult(i + 1, account.email, 'Error', `Token fetch failed: ${tokenResult.error}`);
                                    overallResults.push({email: account.email, status: 'Error', message: `Token fetch failed: ${tokenResult.error}`});
                                    continue; // Skip to next account
                                }
                            } catch (err) {
                                addStep(`Error fetching token for ${account.email}: ${err.message}`, 'danger');
                                addBulkResult(i + 1, account.email, 'Error', `Token fetch error: ${err.message}`);
                                overallResults.push({email: account.email, status: 'Error', message: `Token fetch error: ${err.message}`});
                                continue; // Skip to next account
                            }
                        }
                        
                        // Sub-step 2b: Call the single verification endpoint from email_verifier.py
                        // The /email/api/verify-emails (bulk) endpoint is also an option if we want to send all at once,
                        // but current loop structure is one-by-one. Let's use the single one for clearer step-by-step.
                        addStep(`Attempting verification for ${account.email}...`, 'primary', true);
                        try {
                            const verifyResponse = await fetch('/email/verify-emails', { // Single verify endpoint
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'},
                                body: JSON.stringify({
                                    email: account.email,
                                    password: account.password, // Ensure password is available
                                    access_token: currentAccessToken
                                })
                            });
                            const verifyResult = await verifyResponse.json();

                            if (verifyResult.status === 'success') {
                                addStep(`Successfully verified ${account.email}.`, 'success');
                                addBulkResult(i + 1, account.email, 'Success', 'Verified successfully. Token: ' + (verifyResult.new_access_token ? verifyResult.new_access_token.substring(0,10)+"..." : "N/A"));
                                overallResults.push({email: account.email, status: 'Success', message: 'Verified successfully'});
                            } else {
                                addStep(`Verification failed for ${account.email}: ${verifyResult.error}`, 'danger');
                                addBulkResult(i + 1, account.email, 'Failed', `Verification error: ${verifyResult.error}`);
                                overallResults.push({email: account.email, status: 'Failed', message: `Verification error: ${verifyResult.error}`});
                            }
                        } catch (err) {
                            addStep(`Error during verification for ${account.email}: ${err.message}`, 'danger');
                            addBulkResult(i + 1, account.email, 'Error', `Verification system error: ${err.message}`);
                            overallResults.push({email: account.email, status: 'Error', message: `Verification system error: ${err.message}`});
                        }
                         await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
                    }
                    
                    addStep('Bulk verification process completed.', 'info');
                    updateProgress(100);

                } catch (error) {
                    console.error('Bulk verification error:', error);
                    addStep(`Error during bulk verification: ${error.message}`, 'danger', false);
                    updateProgress(100); // Mark as complete even on error
                } finally {
                    closeProgressBtn.disabled = false;
                }
            });
        }

        function resetModalForBulk(initialMessage) {
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
            initialSpinner.style.display = 'block';
            progressDetails.style.display = 'none'; // Hide individual steps at first
            bulkResultsContainer.style.display = 'none'; // Hide bulk results table
            bulkResultsTableBody.innerHTML = '';
            stepsList.innerHTML = ''; // Clear general steps list
            currentAccountSpan.textContent = '-';
            closeProgressBtn.disabled = true;
            progressMessage.textContent = initialMessage;
        }

        function addStep(message, type = 'info', showSpinner = false) {
            const stepDiv = document.createElement('div');
            stepDiv.className = `list-group-item list-group-item-${type}`;
            stepDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${showSpinner ? '<i class="fas fa-spinner fa-spin me-2"></i>' : ''}${message}</span>
                    <span class="badge bg-${type} rounded-pill">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                </div>`;
            stepsList.appendChild(stepDiv);
            stepsList.scrollTop = stepsList.scrollHeight; // Scroll to bottom
        }

        function addBulkResult(index, email, status, message) {
            const row = bulkResultsTableBody.insertRow();
            row.insertCell().textContent = index;
            row.insertCell().textContent = email;
            const statusCell = row.insertCell();
            statusCell.innerHTML = `<span class="badge bg-${status === 'Success' ? 'success' : (status === 'Failed' ? 'warning' : 'danger')}">${status}</span>`;
            row.insertCell().textContent = message;
        }

        function updateProgress(percentage) {
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        // Enable close button when modal is hidden using X or ESC
        if (progressModalElement) {
            progressModalElement.addEventListener('hidden.bs.modal', function () {
                closeProgressBtn.disabled = false;
                 // Optionally reload the page to see updated account statuses
                if (bulkResultsTableBody.rows.length > 0 || stepsList.children.length > 1) { // if some processing happened
                    // window.location.reload();
                }
            });
        }
    });
</script>
{% endblock %}
