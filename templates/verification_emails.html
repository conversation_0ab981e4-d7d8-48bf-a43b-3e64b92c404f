{% extends 'base.html' %}

{% block title %}Verification Emails - Webook Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Verification Emails</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {# Add this new button for bulk uploading #}
            <a href="{{ url_for('upload_verification_emails') }}" class="btn btn-sm btn-outline-success">
                <i class="fas fa-file-upload me-1"></i> Bulk Upload
            </a>
            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addEmailModal">
                <i class="fas fa-plus me-1"></i> Add New Email
            </button>
        </div>
    </div>
</div>


<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">Total Emails</h6>
                        <h2 class="card-text">{{ stats.total_emails }}</h2>
                    </div>
                    <i class="fas fa-envelope fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">Active Emails</h6>
                        <h2 class="card-text">{{ stats.active_emails }}</h2>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">Burned Emails</h6>
                        <h2 class="card-text">{{ stats.burned_emails }}</h2>
                    </div>
                    <i class="fas fa-fire fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">Success Rate</h6>
                        <h2 class="card-text">{{ stats.avg_success_rate }}%</h2>
                    </div>
                    <i class="fas fa-chart-line fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Verification Emails</h5>
            <div>
                <button class="btn btn-sm btn-outline-success" onclick="filterEmails('active')">Active</button>
                <button class="btn btn-sm btn-outline-danger" onclick="filterEmails('burned')">Burned</button>
                <button class="btn btn-sm btn-outline-secondary" onclick="filterEmails('all')">All</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="emailsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Email</th>
                        <th>IMAP Server</th>
                        <th>Status</th>
                        <th>Stats</th>
                        <th>Last Used</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for email in emails %}
                    <tr class="email-row{{ ' burned' if email.is_burned else '' }}{{ ' inactive' if not email.is_active else '' }}">
                        <td>{{ email.id }}</td>
                        <td>{{ email.email }}</td>
                        <td>{{ email.imap_server }}:{{ email.imap_port }}</td>
                        <td>
                            {% if email.in_use %}
                                <span class="badge bg-warning text-dark">In Use</span>
                            {% elif email.is_burned %}
                                <span class="badge bg-danger">Burned</span>
                            {% elif not email.is_active %}
                                <span class="badge bg-secondary">Inactive</span>
                            {% else %}
                                <span class="badge bg-success">Active</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>Success: {{ email.success_count }}/{{ email.verification_count }}</div>
                            <div class="progress mt-1" style="height: 5px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ email.get_success_rate() }}%;" 
                                     aria-valuenow="{{ email.get_success_rate() }}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100"></div>
                            </div>
                        </td>
                        <td>
                            {% if email.last_used %}
                                {{ email.last_used.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                Never
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary view-email-btn" data-id="{{ email.id }}" data-bs-toggle="modal" data-bs-target="#viewEmailModal">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-{{ 'secondary' if email.is_active else 'success' }} toggle-active-btn" 
                                        data-id="{{ email.id }}" data-active="{{ 'true' if email.is_active else 'false' }}">
                                    <i class="fas fa-{{ 'pause' if email.is_active else 'play' }}"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-{{ 'secondary' if email.is_burned else 'warning' }} toggle-burned-btn" 
                                        data-id="{{ email.id }}" data-burned="{{ 'true' if email.is_burned else 'false' }}">
                                    <i class="fas fa-{{ 'fire-extinguisher' if email.is_burned else 'fire' }}"></i>
                                </button>
                                {% if not email.in_use %}
                                <button class="btn btn-sm btn-outline-danger delete-email-btn" data-id="{{ email.id }}" data-email="{{ email.email }}" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Email Modal -->
<div class="modal fade" id="addEmailModal" tabindex="-1" aria-labelledby="addEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmailModalLabel">Add Verification Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addEmailForm" action="{{ url_for('add_verification_email') }}" method="post">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password (or App Password)</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <div class="form-text">
                            Required for standard IMAP login. Can be left blank if using OAuth2 with a refresh token.
                        </div>
                    </div>
                    <hr>
                    <h6 class="mb-3">Outlook OAuth2 Credentials (Optional)</h6>
                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client ID</label>
                        <input type="text" class="form-control" id="client_id" name="client_id">
                    </div>
                    <div class="mb-3">
                        <label for="refresh_token" class="form-label">Refresh Token</label>
                        <textarea class="form-control" id="refresh_token" name="refresh_token" rows="3"></textarea>
                    </div>
                    </form>
            </div>
            </div>
    </div>
</div>

<!-- View Email Modal -->
<div class="modal fade" id="viewEmailModal" tabindex="-1" aria-labelledby="viewEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewEmailModalLabel">View Verification Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div id="emailDetails" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Email Address:</div>
                        <div class="col-md-8" id="viewEmail"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">IMAP Server:</div>
                        <div class="col-md-8" id="viewImapServer"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">SMTP Server:</div>
                        <div class="col-md-8" id="viewSmtpServer"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Status:</div>
                        <div class="col-md-8" id="viewStatus"></div>
                    </div>
                    <hr>
                    <h6>Verification Statistics</h6>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Total Verifications:</div>
                        <div class="col-md-8" id="viewVerificationCount"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Success Rate:</div>
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="progress flex-grow-1 me-2" style="height: 10px;">
                                    <div class="progress-bar bg-success" id="viewSuccessRateBar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <span id="viewSuccessRate">0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Last Used:</div>
                        <div class="col-md-8" id="viewLastUsed"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="testConnectionBtn">Test Connection</button>
                <button type="button" class="btn btn-warning" id="resetStatsBtn">Reset Stats</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Delete Modal -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the verification email <strong id="deleteEmailAddress"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteEmailForm" action="{{ url_for('delete_verification_email') }}" method="post">
                    <input type="hidden" id="deleteEmailId" name="email_id">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Connection Test Result Modal -->
<div class="modal fade" id="testResultModal" tabindex="-1" aria-labelledby="testResultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testResultModalLabel">Connection Test Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="testingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Testing...</span>
                    </div>
                    <p class="mt-2">Testing connection to email server...</p>
                </div>
                <div id="testSuccess" style="display: none;">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i> Connection Successful!</h5>
                        <p class="mb-0">Successfully connected to the email server.</p>
                    </div>
                </div>
                <div id="testFailure" style="display: none;">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-times-circle me-2"></i> Connection Failed</h5>
                        <p id="errorMessage" class="mb-0"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter emails
        window.filterEmails = function(type) {
            const rows = document.querySelectorAll('.email-row');
            
            rows.forEach(row => {
                if (type === 'all') {
                    row.style.display = '';
                } else if (type === 'active' && !row.classList.contains('burned') && !row.classList.contains('inactive')) {
                    row.style.display = '';
                } else if (type === 'burned' && row.classList.contains('burned')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };
        
        // View email details
        const viewEmailBtns = document.querySelectorAll('.view-email-btn');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const emailDetails = document.getElementById('emailDetails');
        
        viewEmailBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const emailId = this.getAttribute('data-id');
                
                // Reset and show loading state
                loadingSpinner.style.display = 'block';
                emailDetails.style.display = 'none';
                
                // Fetch email details from API
                fetch(`/api/verification-emails/${emailId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Fill in the email details
                            document.getElementById('viewEmail').textContent = data.email.email;
                            document.getElementById('viewImapServer').textContent = `${data.email.imap_server}:${data.email.imap_port}`;
                            document.getElementById('viewSmtpServer').textContent = data.email.smtp_server ? 
                                `${data.email.smtp_server}:${data.email.smtp_port}` : 'Not configured';
                            
                            // Set status
                            let statusHtml = '';
                            if (data.email.in_use) {
                                statusHtml = '<span class="badge bg-warning text-dark">In Use</span>';
                            } else if (data.email.is_burned) {
                                statusHtml = '<span class="badge bg-danger">Burned</span>';
                            } else if (!data.email.is_active) {
                                statusHtml = '<span class="badge bg-secondary">Inactive</span>';
                            } else {
                                statusHtml = '<span class="badge bg-success">Active</span>';
                            }
                            document.getElementById('viewStatus').innerHTML = statusHtml;
                            
                            // Set verification stats
                            document.getElementById('viewVerificationCount').textContent = `${data.email.success_count} / ${data.email.verification_count} (${data.email.success_count} successful, ${data.email.verification_count - data.email.success_count} failed)`;
                            
                            const successRate = data.email.verification_count > 0 ? 
                                (data.email.success_count / data.email.verification_count * 100).toFixed(2) : 0;
                            
                            document.getElementById('viewSuccessRate').textContent = `${successRate}%`;
                            document.getElementById('viewSuccessRateBar').style.width = `${successRate}%`;
                            document.getElementById('viewSuccessRateBar').setAttribute('aria-valuenow', successRate);
                            
                            // Set last used
                            document.getElementById('viewLastUsed').textContent = data.email.last_used ? 
                                new Date(data.email.last_used).toLocaleString() : 'Never';
                            
                            // Set test connection button data
                            document.getElementById('testConnectionBtn').setAttribute('data-id', data.email.id);
                            document.getElementById('resetStatsBtn').setAttribute('data-id', data.email.id);
                            
                            // Hide loading, show details
                            loadingSpinner.style.display = 'none';
                            emailDetails.style.display = 'block';
                        } else {
                            alert(`Error: ${data.message || 'Failed to load email details'}`);
                        }
                    })
                    .catch(error => {
                        alert(`Network error: ${error.message}`);
                    });
            });
        });
        
        // Toggle active status
        const toggleActiveBtns = document.querySelectorAll('.toggle-active-btn');
        
        toggleActiveBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const emailId = this.getAttribute('data-id');
                const isActive = this.getAttribute('data-active') === 'true';
                const newStatus = !isActive;
                
                if (confirm(`Are you sure you want to ${newStatus ? 'activate' : 'deactivate'} this verification email?`)) {
                    fetch('/api/verification-emails/toggle-active', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email_id: emailId,
                            is_active: newStatus
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            window.location.reload();
                        } else {
                            alert(`Error: ${data.message || 'Failed to update status'}`);
                        }
                    })
                    .catch(error => {
                        alert(`Network error: ${error.message}`);
                    });
                }
            });
        });
        
        // Toggle burned status
        const toggleBurnedBtns = document.querySelectorAll('.toggle-burned-btn');
        
        toggleBurnedBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const emailId = this.getAttribute('data-id');
                const isBurned = this.getAttribute('data-burned') === 'true';
                const newStatus = !isBurned;
                
                if (confirm(`Are you sure you want to mark this email as ${newStatus ? 'burned' : 'not burned'}?`)) {
                    fetch('/api/verification-emails/toggle-burned', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email_id: emailId,
                            is_burned: newStatus
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            window.location.reload();
                        } else {
                            alert(`Error: ${data.message || 'Failed to update status'}`);
                        }
                    })
                    .catch(error => {
                        alert(`Network error: ${error.message}`);
                    });
                }
            });
        });
        
        // Delete email confirmation
        const deleteEmailBtns = document.querySelectorAll('.delete-email-btn');
        
        deleteEmailBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const emailId = this.getAttribute('data-id');
                const email = this.getAttribute('data-email');
                
                document.getElementById('deleteEmailAddress').textContent = email;
                document.getElementById('deleteEmailId').value = emailId;
            });
        });
        
        // Test connection button
        const testConnectionBtn = document.getElementById('testConnectionBtn');
        const testResultModal = new bootstrap.Modal(document.getElementById('testResultModal'));
        const testingSpinner = document.getElementById('testingSpinner');
        const testSuccess = document.getElementById('testSuccess');
        const testFailure = document.getElementById('testFailure');
        const errorMessage = document.getElementById('errorMessage');
        
        testConnectionBtn.addEventListener('click', function() {
            const emailId = this.getAttribute('data-id');
            
            // Reset and show test modal
            testingSpinner.style.display = 'block';
            testSuccess.style.display = 'none';
            testFailure.style.display = 'none';
            testResultModal.show();
            
            // Test connection
            fetch(`/api/verification-emails/${emailId}/test`)
                .then(response => response.json())
                .then(data => {
                    testingSpinner.style.display = 'none';
                    
                    if (data.status === 'success') {
                        testSuccess.style.display = 'block';
                    } else {
                        errorMessage.textContent = data.message || 'Unknown error occurred.';
                        testFailure.style.display = 'block';
                    }
                })
                .catch(error => {
                    testingSpinner.style.display = 'none';
                    errorMessage.textContent = `Network error: ${error.message}`;
                    testFailure.style.display = 'block';
                });
        });
        
        // Reset stats button
        const resetStatsBtn = document.getElementById('resetStatsBtn');
        
        resetStatsBtn.addEventListener('click', function() {
            const emailId = this.getAttribute('data-id');
            
            if (confirm('Are you sure you want to reset all statistics for this verification email?')) {
                fetch(`/api/verification-emails/${emailId}/reset-stats`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        window.location.reload();
                    } else {
                        alert(`Error: ${data.message || 'Failed to reset statistics'}`);
                    }
                })
                .catch(error => {
                    alert(`Network error: ${error.message}`);
                });
            }
        });
    });
</script>
{% endblock %}