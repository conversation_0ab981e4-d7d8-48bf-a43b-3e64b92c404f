#!/usr/bin/env python3
"""
Database Migration Script for Webook Admin Dashboard

This script adds the verification_emails table to the database.
Run this script after updating your code to include the new model.

Usage:
    python add_verification_emails_table.py
"""

import sys
import os
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection settings
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_USER = os.getenv('DB_USER', 'webook_user')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'webook_password')
DB_NAME = os.getenv('DB_NAME', 'webook_db')

def run_migration():
    """Run the database migration to add verification_emails table."""
    try:
        # Connect to the database
        connection = mysql.connector.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Check if the table already exists
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = 'verification_emails'
            """, (DB_NAME,))
            
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                print("Table 'verification_emails' already exists in the database.")
                return True
            
            # Create the verification_emails table
            cursor.execute("""
                CREATE TABLE verification_emails (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    imap_server VARCHAR(255) NOT NULL,
                    imap_port INT DEFAULT 993,
                    smtp_server VARCHAR(255),
                    smtp_port INT DEFAULT 587,
                    is_active BOOLEAN DEFAULT TRUE,
                    in_use BOOLEAN DEFAULT FALSE,
                    is_burned BOOLEAN DEFAULT FALSE,
                    last_used DATETIME,
                    verification_count INT DEFAULT 0,
                    success_count INT DEFAULT 0,
                    failure_count INT DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)
            
            connection.commit()
            print("Successfully created 'verification_emails' table.")
            
            # Add the default verification email from environment variables if present
            EMAIL_ADDRESS = os.getenv('EMAIL_ADDRESS')
            EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
            EMAIL_IMAP_SERVER = os.getenv('EMAIL_IMAP_SERVER')
            EMAIL_IMAP_PORT = int(os.getenv('EMAIL_IMAP_PORT', '993'))
            
            if EMAIL_ADDRESS and EMAIL_PASSWORD and EMAIL_IMAP_SERVER:
                cursor.execute("""
                    INSERT INTO verification_emails 
                    (email, password, imap_server, imap_port, is_active)
                    VALUES (%s, %s, %s, %s, TRUE)
                """, (EMAIL_ADDRESS, EMAIL_PASSWORD, EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT))
                
                connection.commit()
                print(f"Added default verification email {EMAIL_ADDRESS} from environment variables.")
            
            # Verify the table was created
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = 'verification_emails'
            """, (DB_NAME,))
            
            table_created = cursor.fetchone()[0]
            
            if table_created:
                print("Verified that 'verification_emails' table was created successfully.")
                return True
            else:
                print("Error: 'verification_emails' table was not created correctly.")
                return False
                
    except Error as e:
        print(f"Database error: {str(e)}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("Database connection closed.")

if __name__ == "__main__":
    print("Starting migration to add 'verification_emails' table...")
    
    if run_migration():
        print("Migration completed successfully.")
        sys.exit(0)
    else:
        print("Migration failed.")
        sys.exit(1)