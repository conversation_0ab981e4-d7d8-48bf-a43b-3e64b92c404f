#!/bin/bash
# Setup script for MySQL and Webook Login API service
# Run this script as sudo on Ubuntu

# Update package lists
apt update

# Install MySQL Server
apt install -y mysql-server python3-pip

# Secure MySQL installation (non-interactive)
# First, we need to access MySQL as root without a password (using sudo)
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'webook_root_password';"

# Now we can use the root password for subsequent commands
mysql -u root -pwebook_root_password -e "DELETE FROM mysql.user WHERE User='';"
mysql -u root -pwebook_root_password -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
mysql -u root -pwebook_root_password -e "DROP DATABASE IF EXISTS test;"
mysql -u root -pwebook_root_password -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
mysql -u root -pwebook_root_password -e "FLUSH PRIVILEGES;"

# Create database and user for the application
mysql -u root -pwebook_root_password -e "CREATE DATABASE webook_db;"
mysql -u root -pwebook_root_password -e "CREATE USER 'webook_user'@'localhost' IDENTIFIED BY 'webook_password';"
mysql -u root -pwebook_root_password -e "GRANT ALL PRIVILEGES ON webook_db.* TO 'webook_user'@'localhost';"
mysql -u root -pwebook_root_password -e "FLUSH PRIVILEGES;"

# Install Python dependencies
pip3 install flask flask-sqlalchemy pymysql cryptography httpx

# Create service file for the application
cat > /etc/systemd/system/webook-login-api.service << EOF
[Unit]
Description=Webook Login API Service
After=network.target mysql.service

[Service]
User=www-data
WorkingDirectory=/opt/webook-login-api
ExecStart=/usr/bin/python3 /opt/webook-login-api/app.py
Restart=always
Environment="DATABASE_URI=mysql+pymysql://webook_user:webook_password@localhost/webook_db"
Environment="SECRET_KEY=change_this_to_a_secure_random_key"

[Install]
WantedBy=multi-user.target
EOF

# Create application directory
mkdir -p /opt/webook-login-api
chown www-data:www-data /opt/webook-login-api

echo "MySQL setup complete! Now copy your Webook Login API code to /opt/webook-login-api/app.py"
echo "Then initialize the database with: flask --app /opt/webook-login-api/app.py init-db"
echo "Finally, enable and start the service: systemctl enable webook-login-api.service && systemctl start webook-login-api.service"