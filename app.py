#!/usr/bin/env python3
"""
Webook Admin Dashboard and API

A comprehensive Flask application for managing Webook accounts:
- Web admin interface for account management
- Bulk account upload functionality
- Token retrieval system with rotation
- Account statistics and monitoring
- REST API endpoints for programmatic access

This application builds on the existing login API functionality
and adds additional features for administration.

Setup:
1. Install requirements:
   pip install flask flask-sqlalchemy pymysql cryptography httpx flask-login flask-wtf

2. Configure MySQL connection in config.py or environment variables.

3. Initialize database:
   flask init-db

4. Create admin user:
   flask create-admin

5. Run the application:
   flask run --host=0.0.0.0 --port=5000
"""

import os
import csv
import json
import time
import random
import logging
import hashlib
import asyncio
from io import String<PERSON>
from functools import wraps
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any, List
from models import db, User, Account, Setting, VerificationEmail
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, abort, Response
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import httpx
from email_verifier import email_verifier_bp, verify_webook_account
from dotenv import load_dotenv
from verify_email import verify_webook_account as verify_email_webook_account # Renamed to avoid conflict
# Import captcha bypass function (you'll need to implement or modify this)
from bypass_captcha import recaptcha1


load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.register_blueprint(email_verifier_bp, url_prefix='/email')


# Configuration
class Config:
    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI',
        'mysql+pymysql://webook_user:webook_password@localhost/webook_db'
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_change_in_production')

    # Upload folder
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {'txt', 'csv'}

    # Token validity period (7 days in seconds)
    TOKEN_VALIDITY_PERIOD = 7 * 24 * 60 * 60

    # API settings
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

    # Admin default credentials (for initial setup only)
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', 'admin')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'changeme')

app.config.from_object(Config)

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize database
db.init_app(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Database initialization command
@app.cli.command('init-db')
def init_db_command():
    """Initialize the database."""
    db.create_all()
    print('Initialized the database.')

# Create admin user command
@app.cli.command('create-admin')
def create_admin_command():
    """Create an admin user."""
    username = app.config['ADMIN_USERNAME']
    password = app.config['ADMIN_PASSWORD']

    existing_user = User.query.filter_by(username=username).first()
    if existing_user:
        print(f"User '{username}' already exists.")
        return

    admin_user = User(username=username, is_admin=True)
    admin_user.set_password(password)
    db.session.add(admin_user)
    db.session.commit()
    print(f"Created admin user '{username}' with password '{password}'.")
    print("Please change this password immediately after first login.")

# Helper functions
async def random_user_agent():
    """Generate a user agent."""
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0"

async def generate_signature(email):
    """Generate a signature for the login request."""
    random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=20))
    md5_hash = hashlib.md5(f"{email}e0q76ti7ikioqnqb{random_str}".encode()).hexdigest()
    return f"{md5_hash}|{random_str}"

async def login_to_webook(email: str, password: str, proxy: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
    """
    Log into Webook and extract the access token.

    Args:
        email: User's email address
        password: User's password
        proxy: Optional proxy in format ip:port or user:pass@ip:port

    Returns:
        Tuple of (success, response_data)
        If successful, response_data contains the access token
        If failed, response_data contains error information
    """
    # Setup proxy if provided
    proxy_url = None
    if proxy:
        if '@' in proxy:  # With authentication
            proxy_url = f"http://{proxy}"
        else:  # Without authentication
            proxy_url = f"http://{proxy}"

    # Generate user agent
    ua = await random_user_agent()

    try:
        # Solve captcha
        print(f"Solving captcha for {email}...")
        captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcvYHooAAAAAC-G46bpymJKtIwfDQpg9DsHPMpL&co=aHR0cHM6Ly93ZWJvb2suY29tOjQ0Mw..&hl=en&v=IyZ984yGrXrBd6ihLOYGwy9X&size=invisible&cb=b7e5fsomtcd"
        cap = await recaptcha1(captcha_url, False, ua)
        if not cap:
            return False, {"error": "Failed to solve captcha"}

        # Generate signature
        signature = await generate_signature(email)

        # Create HTTP client with proxy if available
        client_kwargs = {"follow_redirects": True}
        # set proxy_url to this if no proxy_url ucegtvkm-rotate:<EMAIL>:80
        # Using the globally defined proxy from bypass_captcha or config if available
        # For simplicity, directly using a hardcoded one for now as in the original code block
        # In a real app, this should come from config.
        effective_proxy = proxy_url or os.getenv('DEFAULT_PROXY', "http://ucegtvkm-rotate:<EMAIL>:80")
        if '//' not in effective_proxy:
            effective_proxy = f"http://{effective_proxy}"
        if effective_proxy:
            client_kwargs["proxies"] = {
                "http://": effective_proxy,
                "https://": effective_proxy
                }


        async with httpx.AsyncClient(**client_kwargs) as client:
            # Prepare login payload
            payload = {
                "email": email,
                "password": password,
                "captcha": cap,
                "signature": signature,
                "app_source": "rs",
                "login_with": "email",
                "lang": "en"
            }

            # Prepare headers
            headers = {
                "accept": "application/json",
                "accept-language": "en-US,en;q=0.9",
                "authorization": "Bearer",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "dnt": "1",
                "origin": "https://webook.com",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "token": "e9aac1f2f0b6c07d6be070ed14829de684264278359148d6a582ca65a50934d2", # This seems like a static token, might need review
                "user-agent": ua
            }

            # Send login request
            print(f"Sending login request for {email}...")
            response = await client.post(
                "https://api.webook.com/api/v2/login",
                headers=headers,
                json=payload,
            )

            # Process response
            try:
                response_data = response.json()

                # Check if login was successful and access token exists
                if response_data.get('data', {}).get('access_token'):
                    access_token = response_data['data']['access_token']
                    print(f"Login successful for {email}! Access token retrieved.")
                    return True, {"access_token": access_token}
                else:
                    # Try to extract error message from different possible locations in the response
                    error_msg = response_data.get('message', None)

                    if not error_msg and 'error' in response_data:
                        if isinstance(response_data['error'], dict) and 'email' in response_data['error']:
                            error_msg = response_data['error']['email']
                        elif isinstance(response_data['error'], str):
                            error_msg = response_data['error']

                    if not error_msg:
                        error_msg = 'Unknown error'

                    print(f"Login failed for {email}. Error: {error_msg}")
                    return False, {"error": error_msg, "response": response_data}

            except json.JSONDecodeError:
                print(f"Failed to parse JSON response for {email}.")
                return False, {"error": "Invalid JSON response", "raw_response": response.text[:500]}

    except Exception as e:
        print(f"Exception during login for {email}: {str(e)}")
        return False, {"error": str(e)}

def allowed_file(filename):
    """Check if a file has an allowed extension."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def admin_required(f):
    """Decorator for routes that require admin access."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403)  # Forbidden
        return f(*args, **kwargs)
    return decorated_function

def get_account_stats():
    """Get overall account statistics with enhanced verification info."""
    total_accounts = Account.query.count()
    dead_accounts = Account.query.filter_by(is_dead=True).count()
    verified_accounts = Account.query.filter_by(is_verified=True).count()
    # Correctly count unverified accounts that are not dead
    unverified_accounts = Account.query.filter_by(is_verified=False, is_dead=False).count()
    
    # Get accounts with valid tokens (not expired)
    valid_tokens = Account.query.filter(
        Account.access_token.isnot(None),
        Account.token_retrieved_at.isnot(None),
        Account.token_retrieved_at > datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])
    ).count()
    
    # Get verified accounts with valid tokens
    verified_with_valid_tokens = Account.query.filter(
        Account.is_verified == True,
        Account.access_token.isnot(None),
        Account.token_retrieved_at.isnot(None),
        Account.token_retrieved_at > datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])
    ).count()
    
    # Accounts waiting for verification (unverified and not dead)
    waiting_for_verification = unverified_accounts # This is the same as unverified_accounts now
    
    # Get verification email stats
    verification_emails = VerificationEmail.query.all()
    active_verification_emails = sum(1 for email in verification_emails if email.is_active and not email.is_burned)
    burned_verification_emails = sum(1 for email in verification_emails if email.is_burned)
    
    # Calculate verification success rate
    # Denominator should be total attempts or total processable accounts
    # For now, using (verified + unverified_and_not_dead) as a proxy for accounts that underwent or will undergo verification
    total_verifiable = verified_accounts + unverified_accounts 
    verification_success_rate = 0
    if total_verifiable > 0: # Avoid division by zero
        verification_success_rate = round((verified_accounts / total_verifiable) * 100, 2)
    
    available_accounts = total_accounts - dead_accounts

    return {
        "total_accounts": total_accounts,
        "available_accounts": available_accounts,
        "dead_accounts": dead_accounts,
        "verified_accounts": verified_accounts,
        "unverified_accounts": unverified_accounts, # unverified and not dead
        "valid_tokens": valid_tokens,
        "verified_with_valid_tokens": verified_with_valid_tokens,
        "waiting_for_verification": waiting_for_verification,
        "verification_success_rate": verification_success_rate,
        "verification_emails": {
            "total": len(verification_emails),
            "active": active_verification_emails,
            "burned": burned_verification_emails
        }
    }


# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page."""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            next_page = request.args.get('next', url_for('dashboard'))
            return redirect(next_page)

        flash('Invalid username or password', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Logout route."""
    logout_user()
    return redirect(url_for('login'))

# Web interface routes
@app.route('/')
@login_required
def dashboard():
    """Admin dashboard with enhanced verification statistics."""
    stats = get_account_stats()
    recent_accounts = Account.query.order_by(Account.created_at.desc()).limit(10).all()
    
    # Get recent verified accounts
    recent_verified = Account.query.filter_by(is_verified=True).order_by(Account.updated_at.desc()).limit(5).all()

    return render_template(
        'dashboard.html',
        stats=stats,
        recent_accounts=recent_accounts,
        recent_verified=recent_verified
    )

@app.route('/accounts', methods=['GET'])
@login_required
def accounts():
    """Account management page."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    status_filter = request.args.get('status', 'all')

    query = Account.query

    if status_filter == 'dead':
        query = query.filter_by(is_dead=True)
    elif status_filter == 'alive': # Alive means not dead
        query = query.filter_by(is_dead=False)
    elif status_filter == 'verified':
        query = query.filter_by(is_verified=True, is_dead=False) # Verified and not dead
    elif status_filter == 'unverified': # Unverified and not dead
        query = query.filter_by(is_verified=False, is_dead=False)
    elif status_filter == 'with_token':
        query = query.filter(Account.access_token.isnot(None), Account.is_dead==False)


    accounts_pagination = query.order_by(Account.id.desc()).paginate(page=page, per_page=per_page)

    return render_template(
        'accounts.html',
        accounts=accounts_pagination,
        status_filter=status_filter
    )

@app.route('/accounts/<int:account_id>')
@login_required
def account_details(account_id):
    """Account details page."""
    account = Account.query.get_or_404(account_id)
    return render_template('account_details.html', account=account)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
@admin_required
def upload_accounts():
    """Upload accounts page."""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file part', 'danger')
            return redirect(request.url)

        file = request.files['file']

        if file.filename == '':
            flash('No selected file', 'danger')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            # Process the file
            stream = StringIO(file.stream.read().decode('utf-8'))

            # Determine the delimiter (comma, colon, or tab)
            sample = stream.read(1024)
            stream.seek(0)

            if ',' in sample:
                delimiter = ','
            elif ':' in sample:
                delimiter = ':'
            elif '\t' in sample:
                delimiter = '\t'
            else:
                delimiter = ','  # Default to comma if no delimiter is detected

            reader = csv.reader(stream, delimiter=delimiter)

            # Count added and updated accounts
            added = 0
            updated = 0

            # Process each row
            for row in reader:
                if len(row) >= 2:  # Ensure we have at least email and password
                    email = row[0].strip()
                    password = row[1].strip()

                    # Check if account already exists
                    existing_account = Account.query.filter_by(email=email).first()

                    if existing_account:
                        # Update existing account
                        existing_account.password = password
                        existing_account.updated_at = datetime.utcnow()
                        updated += 1
                    else:
                        # Create new account
                        new_account = Account(
                            email=email,
                            password=password
                        )
                        db.session.add(new_account)
                        added += 1

            try:
                db.session.commit()
                flash(f'Successfully processed file. Added {added} new accounts, updated {updated} existing accounts.', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Error processing file: {str(e)}', 'danger')

            return redirect(url_for('accounts'))

    return render_template('upload.html')

@app.route('/upload-verification-emails', methods=['GET', 'POST'])
@login_required
@admin_required
def upload_verification_emails():
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file part', 'danger')
            return redirect(request.url)

        file = request.files['file']

        if file.filename == '':
            flash('No selected file', 'danger')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            stream = StringIO(file.stream.read().decode('utf-8'))
            reader = csv.reader(stream, delimiter='|')

            added = 0
            updated = 0
            errors = 0

            for row in reader:
                try:
                    if len(row) >= 4:
                        email, password, refresh_token, client_id = [item.strip() for item in row]

                        # Default IMAP server for Outlook
                        imap_server = "outlook.office365.com"
                        imap_port = 993

                        # Check if account already exists
                        existing_email = VerificationEmail.query.filter_by(email=email).first()

                        if existing_email:
                            # Update existing email
                            existing_email.password = password or existing_email.password
                            existing_email.refresh_token = refresh_token
                            existing_email.client_id = client_id
                            updated += 1
                        else:
                            # Create new verification email
                            new_email = VerificationEmail(
                                email=email,
                                password=password,
                                refresh_token=refresh_token,
                                client_id=client_id,
                                imap_server=imap_server,
                                imap_port=imap_port,
                                is_active=True
                            )
                            db.session.add(new_email)
                            added += 1
                    else:
                        errors += 1
                except Exception as e:
                    errors += 1
                    print(f"Error processing row: {row}. Error: {e}")

            try:
                db.session.commit()
                flash(f'Successfully processed file. Added {added}, updated {updated}, skipped {errors} invalid rows.', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Database error: {str(e)}', 'danger')

            return redirect(url_for('verification_emails'))

    return render_template('upload_verification_emails.html')

@app.route('/get-tokens', methods=['GET', 'POST'])
@login_required
@admin_required
def get_tokens():
    """Enhanced page to retrieve tokens with verification as top priority."""
    stats = get_account_stats()
    
    if request.method == 'POST':
        count = request.form.get('count', type=int)
        proxy_str = request.form.get('proxy', '') # Renamed to avoid conflict
        verify_first = request.form.get('verify_first', 'off') == 'on'
        
        if count == -1:
            # Get all accounts that need either verification or tokens
            if verify_first:
                count = Account.query.filter_by(is_verified=False, is_dead=False).count()
            else: # Original logic: all accounts needing tokens
                count = Account.query.filter_by(access_token=None, is_dead=False).count()
        
        if not count or count <= 0:
            flash('Please enter a valid count or no accounts need processing.', 'danger')
            return redirect(request.url)

        processed_results = []

        # Modified logic: Prioritize verification
        if verify_first:
            # Get unverified accounts first, regardless of token status
            accounts_to_process = Account.query.filter_by(
                is_dead=False,
                is_verified=False
            ).limit(count).all()
            
            # Process these accounts
            for account in accounts_to_process:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # First, check if account has a token
                if not account.access_token:
                    # Get a token first
                    success, result = loop.run_until_complete(
                        login_to_webook(account.email, account.password, proxy_str)
                    )
                    
                    account.last_login_attempt = datetime.utcnow()
                    account.login_attempts += 1
                    
                    if success:
                        account.access_token = result["access_token"]
                        account.token_retrieved_at = datetime.utcnow()
                        account.is_dead = False
                    else:
                        # Mark as dead if can't get token
                        error_msg = result.get("error", "")
                        if "locked" in error_msg.lower() or "banned" in error_msg.lower() or "invalid credentials" in error_msg.lower():
                            account.is_dead = True
                        
                        processed_results.append({
                            "email": account.email,
                            "status": "error",
                            "error": f"Failed to get token: {error_msg}",
                            "verification": {"attempted": False, "success": None, "result": None}
                        })
                        loop.close()
                        continue # Move to next account if token retrieval failed
                
                # Now verify the account (ensure we have a token)
                if account.access_token:
                    try:
                        print(f"Attempting to verify account: {account.email}")
                        # Using the renamed import for clarity
                        verification_success, verification_result = loop.run_until_complete(
                            verify_email_webook_account(account.email, account.password, account.access_token)
                        )
                        
                        if verification_success:
                            print(f"Verification successful for {account.email}")
                            # verify_email_webook_account should return the new token
                            account.access_token = verification_result 
                            account.token_retrieved_at = datetime.utcnow()
                            account.is_verified = True
                            
                            processed_results.append({
                                "email": account.email,
                                "status": "success",
                                "token": account.access_token,
                                "verification": {
                                    "attempted": True,
                                    "success": True,
                                    "result": "Successfully verified and token updated"
                                }
                            })
                        else:
                            print(f"Verification failed for {account.email}: {verification_result}")
                            # If verification fails, the account might be dead or have issues
                            if "invalid credentials" in str(verification_result).lower() or "account locked" in str(verification_result).lower():
                                account.is_dead = True
                            processed_results.append({
                                "email": account.email,
                                "status": "partial", # Token might be valid, but verification failed
                                "token": account.access_token,
                                "verification": {
                                    "attempted": True,
                                    "success": False,
                                    "result": verification_result
                                }
                            })
                    except Exception as e:
                        print(f"Error during verification for {account.email}: {str(e)}")
                        processed_results.append({
                            "email": account.email,
                            "status": "error",
                            "error": str(e),
                            "verification": {
                                "attempted": True,
                                "success": False,
                                "result": str(e)
                            }
                        })
                else: # Should not happen if logic above is correct, but as a fallback
                     processed_results.append({
                        "email": account.email,
                        "status": "error",
                        "error": "Token was not available for verification step.",
                        "verification": {"attempted": False, "success": None, "result": None}
                    })

                loop.close()
                
            # Save all changes
            try:
                db.session.commit()
                flash(f'Successfully processed {len(processed_results)} accounts with verification priority.', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Database error: {str(e)}', 'danger')
                
        else: # Original token-first logic (verify_first is False)
            accounts_to_process = []
            # Find accounts that need tokens
            accounts_query = Account.query.filter_by(is_dead=False)
            
            # Get accounts with no token first
            accounts_no_token = accounts_query.filter(Account.access_token.is_(None)).limit(count).all()
            accounts_to_process.extend(accounts_no_token)
            
            # If we need more, get accounts with expired tokens
            if len(accounts_to_process) < count:
                remaining = count - len(accounts_to_process)
                expired_time = datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])
                accounts_expired = accounts_query.filter(
                    Account.access_token.isnot(None),
                    Account.token_retrieved_at < expired_time
                ).order_by(Account.token_retrieved_at).limit(remaining).all()
                accounts_to_process.extend(accounts_expired)

            # If we still need more, get accounts with valid tokens but oldest first (force refresh)
            if len(accounts_to_process) < count:
                remaining = count - len(accounts_to_process)
                valid_time = datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])
                accounts_valid_oldest = accounts_query.filter(
                    Account.access_token.isnot(None),
                    Account.token_retrieved_at >= valid_time # Should be all valid tokens
                ).order_by(Account.token_retrieved_at).limit(remaining).all()
                accounts_to_process.extend(accounts_valid_oldest)

            # Process accounts and retrieve tokens
            for account in accounts_to_process:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                success, result = loop.run_until_complete(login_to_webook(account.email, account.password, proxy_str))
                
                account.last_login_attempt = datetime.utcnow()
                account.login_attempts += 1

                if success:
                    account.access_token = result["access_token"]
                    account.token_retrieved_at = datetime.utcnow()
                    account.is_dead = False # Reset dead status on successful login
                    
                    processed_results.append({
                        "email": account.email,
                        "status": "success",
                        "token": account.access_token,
                        "verification": { # Verification not attempted in this branch
                            "attempted": False,
                            "success": None,
                            "result": "Token retrieved, verification not prioritized"
                        }
                    })
                else:
                    error_msg = result.get("error", "")
                    if "locked" in error_msg.lower() or "banned" in error_msg.lower() or "invalid credentials" in error_msg.lower():
                        account.is_dead = True

                    processed_results.append({
                        "email": account.email,
                        "status": "error",
                        "error": error_msg,
                        "verification": {
                            "attempted": False,
                            "success": None,
                            "result": None
                        }
                    })
                
                loop.close()

            try:
                db.session.commit()
                flash(f'Successfully processed {len(processed_results)} accounts (token-only mode).', 'success')
            except Exception as e:
                db.session.rollback()
                flash(f'Database error: {str(e)}', 'danger')

        return render_template('token_results.html', results=processed_results)

    return render_template('get_tokens.html', stats=stats)


@app.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    """Application settings page."""
    # Handle password change
    if request.method == 'POST' and 'change_password' in request.form:
        current_password = request.form.get('currentPassword')
        new_password = request.form.get('newPassword')
        confirm_password = request.form.get('confirmPassword')

        if not current_password or not new_password or not confirm_password:
            flash('All password fields are required', 'danger')
            return redirect(url_for('settings'))

        if new_password != confirm_password:
            flash('New passwords do not match', 'danger')
            return redirect(url_for('settings'))

        # Get the current user
        user = User.query.get(current_user.id)

        # Check if current password is correct
        if not user.check_password(current_password):
            flash('Current password is incorrect', 'danger')
            return redirect(url_for('settings'))

        # Update password
        user.set_password(new_password)
        db.session.commit()

        flash('Password changed successfully', 'success')
        return redirect(url_for('settings'))

    # Handle application settings
    elif request.method == 'POST' and 'save_settings' in request.form:
        # Get form values
        token_validity_period_days = request.form.get('tokenValidityPeriod')
        default_proxy_setting = request.form.get('defaultProxy') # Renamed to avoid conflict
        max_login_attempts = request.form.get('maxLoginAttempts')

        # Validate inputs
        try:
            # Convert to appropriate types
            token_validity_period = int(token_validity_period_days) * 24 * 60 * 60  # Convert days to seconds
            max_login_attempts_val = int(max_login_attempts) # Renamed

            # Save settings
            Setting.set('TOKEN_VALIDITY_PERIOD', str(token_validity_period))
            Setting.set('DEFAULT_PROXY', default_proxy_setting)
            Setting.set('MAX_LOGIN_ATTEMPTS', str(max_login_attempts_val))

            # Update app config (important for runtime)
            app.config['TOKEN_VALIDITY_PERIOD'] = token_validity_period
            # Potentially update other app.config values if they are used directly elsewhere

            flash('Settings saved successfully', 'success')
        except ValueError:
            flash('Invalid input values for settings', 'danger')

        return redirect(url_for('settings'))

    # Get current settings
    token_validity_period_current = int(Setting.get('TOKEN_VALIDITY_PERIOD', app.config['TOKEN_VALIDITY_PERIOD'])) // (24 * 60 * 60)  # Convert seconds to days
    default_proxy_current = Setting.get('DEFAULT_PROXY', '')
    max_login_attempts_current = int(Setting.get('MAX_LOGIN_ATTEMPTS', '3')) # Default to 3 if not set

    return render_template('settings.html',
                           token_validity_period=token_validity_period_current,
                           default_proxy=default_proxy_current,
                           max_login_attempts=max_login_attempts_current)

# API routes
@app.route('/api/login', methods=['POST'])
def api_login():
    """
    API endpoint to login to Webook and get an access token.

    Required POST parameters:
    - email: User's email
    - password: User's password

    Optional parameters:
    - proxy: Proxy in format ip:port or user:pass@ip:port

    Returns:
    - JSON with access_token on success
    - JSON with error message on failure
    """
    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    email = data.get('email')
    password = data.get('password')
    proxy_param = data.get('proxy') # Renamed to avoid conflict

    if not email or not password:
        return jsonify({"error": "Email and password are required"}), 400

    # Run the async login function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    success, result = loop.run_until_complete(login_to_webook(email, password, proxy_param))
    loop.close()

    if success:
        # Store the token in the database
        existing_account = Account.query.filter_by(email=email).first()

        if existing_account:
            # Update existing account
            existing_account.password = password # Update password in case it changed
            existing_account.access_token = result["access_token"]
            existing_account.token_retrieved_at = datetime.utcnow()
            existing_account.last_login_attempt = datetime.utcnow()
            existing_account.login_attempts += 1
            existing_account.is_dead = False # Mark as not dead on successful login
            existing_account.updated_at = datetime.utcnow()
        else:
            # Create new account
            new_account = Account(
                email=email,
                password=password,
                access_token=result["access_token"],
                token_retrieved_at=datetime.utcnow(),
                last_login_attempt=datetime.utcnow(),
                login_attempts=1
            )
            db.session.add(new_account)

        try:
            db.session.commit()
            print(f"Saved token to database for {email}")
        except Exception as e:
            db.session.rollback()
            print(f"Database error: {str(e)}")
            return jsonify({"error": f"Database error: {str(e)}"}), 500

        # Return the access token
        return jsonify({
            "status": "success",
            "email": email,
            "access_token": result["access_token"]
        })
    else:
        # If login failed, update account status in DB
        existing_account = Account.query.filter_by(email=email).first()
        if existing_account:
            existing_account.last_login_attempt = datetime.utcnow()
            existing_account.login_attempts += 1
            error_msg_detail = result.get("error", "")
            if "locked" in error_msg_detail.lower() or "banned" in error_msg_detail.lower() or "invalid credentials" in error_msg_detail.lower():
                existing_account.is_dead = True
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Database error updating failed login: {str(e)}")
        
        # Return error
        return jsonify({
            "status": "error",
            "email": email,
            "error": result.get("error", "Unknown error"),
            "details": result # Include full response for debugging if needed
        }), 401


@app.route('/api/token/<email>', methods=['GET'])
def get_token(email):
    """
    API endpoint to get a stored token for an email.

    Returns:
    - JSON with account details including access_token
    - 404 if account not found
    """
    account = Account.query.filter_by(email=email).first()

    if not account:
        return jsonify({"error": "Account not found"}), 404

    return jsonify({
        "status": "success",
        "account": account.to_dict() # Ensure to_dict() includes all relevant fields
    })

@app.route('/api/token', methods=['GET'])
def get_rotating_token():
    """
    API endpoint to get a single rotating token.

    This will return a valid token from the pool, prioritizing:
    1. Verified accounts with valid tokens that have been used the least recently

    Returns:
    - JSON with token details
    - 404 if no valid token found
    """
    # Check if we should only use verified accounts
    verified_only = request.args.get('verified_only', 'true').lower() == 'true'
    
    # Get a valid token
    valid_time_threshold = datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])

    query = Account.query.filter(
        Account.is_dead == False,
        Account.access_token.isnot(None),
        Account.token_retrieved_at.isnot(None),
        Account.token_retrieved_at >= valid_time_threshold # Token must be valid
    )
    
    # Apply verified filter if requested
    if verified_only:
        query = query.filter(Account.is_verified == True)
    
    # Order by least recently used (oldest token_retrieved_at)
    account = query.order_by(Account.token_retrieved_at.asc()).first()


    if not account:
        return jsonify({"error": "No valid token found" + (" for verified accounts" if verified_only else "")}), 404

    # Update token retrieval time to implement rotation (mark as recently used)
    # This field is actually 'token_retrieved_at', so updating it means it's the newest.
    # For rotation, we want to pick the one that was 'retrieved' longest ago.
    # The query `order_by(Account.token_retrieved_at.asc()).first()` already does this.
    # We might want a `last_used_at` field for true rotation if `token_retrieved_at` means when it was *obtained*.
    # For now, assuming `token_retrieved_at` update is for marking it "refreshed" or "checked".
    # If this API is for *consuming* tokens, then a `last_consumed_at` field would be better.
    # Let's assume for now the current behavior is intended: pick oldest valid, then update its retrieved_at.
    # This means this token will go to the back of the queue for the next pick.
    
    # account.token_retrieved_at = datetime.utcnow() # This would make it the newest.
    # Let's add a new field `last_accessed_api_at` for this specific API usage if strict rotation is needed.
    # Or, if this API is *refreshing* the token, then updating token_retrieved_at is correct.
    # Given the name "get_rotating_token", it implies consumption.
    # For now, let's not update token_retrieved_at here to ensure it keeps its "age" for rotation.
    # If the goal is to mark it as "used now", a different field or strategy is needed.
    # The original code *does* update token_retrieved_at. This makes it the "newest" token.
    # This means it will be picked last next time. This is a valid rotation strategy.
    account.token_retrieved_at = datetime.utcnow()


    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": f"Database error: {str(e)}"}), 500

    # Get token expiration information
    expiration_info = account.get_token_expiration()

    return jsonify({
        "status": "success",
        "email": account.email,
        "is_verified": account.is_verified,
        "access_token": account.access_token,
        "retrieved_at": account.token_retrieved_at.isoformat(), # This will be the new "now" time
        "valid": expiration_info["valid"],
        "expires_in_seconds": expiration_info["expires_in_seconds"],
        "expires_at_epoch": expiration_info["expires_at_epoch"]
    })


@app.route('/api/tokens', methods=['GET'])
def get_multiple_tokens():
    """
    API endpoint to get multiple tokens at once.

    Query parameters:
    - count: Number of tokens to retrieve (default: 1, max: 50)
    - valid_only: Whether to only return valid tokens (default: true)
    - verified_only: Whether to only return tokens from verified accounts (default: true)

    Returns:
    - JSON with list of tokens
    - 404 if no tokens found
    """
    # Get query parameters
    count = request.args.get('count', 1, type=int)
    valid_only = request.args.get('valid_only', 'true').lower() == 'true'
    verified_only = request.args.get('verified_only', 'true').lower() == 'true'

    count = min(max(1, count), 50) # Cap count

    # Query for accounts
    accounts_query = Account.query.filter(Account.is_dead == False)
    
    # Apply verified filter if requested
    if verified_only:
        accounts_query = accounts_query.filter(Account.is_verified == True)

    # If valid_only is true, only return accounts with valid tokens
    if valid_only:
        valid_time_threshold = datetime.utcnow() - timedelta(seconds=app.config['TOKEN_VALIDITY_PERIOD'])
        accounts_query = accounts_query.filter(
            Account.access_token.isnot(None),
            Account.token_retrieved_at.isnot(None),
            Account.token_retrieved_at >= valid_time_threshold
        )
    else: # valid_only is false, return any account with a token (even if expired)
        accounts_query = accounts_query.filter(
            Account.access_token.isnot(None)
        )
    
    # Order by oldest retrieved (for rotation) and limit
    selected_accounts = accounts_query.order_by(Account.token_retrieved_at.asc()).limit(count).all()


    if not selected_accounts:
        return jsonify({"error": "No tokens found" + (" for verified accounts" if verified_only else "")}), 404

    # Update token retrieval time for all accounts to implement rotation
    now = datetime.utcnow()
    for acc in selected_accounts: # Renamed to avoid conflict
        acc.token_retrieved_at = now

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": f"Database error: {str(e)}"}), 500

    # Prepare response
    tokens_response = []
    for acc_resp in selected_accounts: # Renamed
        expiration_info = acc_resp.get_token_expiration()
        tokens_response.append({
            "email": acc_resp.email,
            "is_verified": acc_resp.is_verified,
            "access_token": acc_resp.access_token,
            "retrieved_at": acc_resp.token_retrieved_at.isoformat(), # Will be 'now'
            "valid": expiration_info["valid"], # Validity based on previous retrieved_at
            "expires_in_seconds": expiration_info["expires_in_seconds"],
            "expires_at_epoch": expiration_info["expires_at_epoch"]
        })

    return jsonify({
        "status": "success",
        "count": len(tokens_response),
        "tokens": tokens_response
    })

@app.route('/api/account/<email>', methods=['DELETE'])
@login_required # Added protection
@admin_required # Added protection
def delete_account(email):
    """
    API endpoint to delete an account by email.

    Returns:
    - JSON with success message
    - 404 if account not found
    """
    account_to_delete = Account.query.filter_by(email=email).first() # Renamed

    if not account_to_delete:
        return jsonify({"error": "Account not found"}), 404

    try:
        db.session.delete(account_to_delete)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": f"Database error: {str(e)}"}), 500

    return jsonify({
        "status": "success",
        "message": f"Account {email} deleted successfully"
    })

@app.route('/api/stats', methods=['GET'])
@login_required # Added protection
def get_stats():
    """
    API endpoint to get account statistics.

    Returns:
    - JSON with account statistics
    """
    stats_data = get_account_stats() # Renamed
    return jsonify({
        "status": "success",
        "stats": stats_data
    })


@app.route('/api/accounts', methods=['GET'])
@login_required # Added protection
def api_get_accounts():
    """
    API endpoint to get accounts with filtering options.
    
    Query parameters:
    - status: Filter by status (all, dead, alive, verified, unverified, with_token)
    - limit: Maximum number of accounts to return (default: 10, max: 100)
    - offset: Offset for pagination (default: 0)
    
    Returns:
    - JSON with list of accounts
    """
    # Get query parameters
    status_filter_api = request.args.get('status', 'all') # Renamed
    limit_api = min(int(request.args.get('limit', 10)), 100)  # Cap at 100
    offset_api = int(request.args.get('offset', 0))
    
    # Build query
    query_api = Account.query # Renamed
    
    # Apply filters
    if status_filter_api == 'dead':
        query_api = query_api.filter_by(is_dead=True)
    elif status_filter_api == 'alive':
        query_api = query_api.filter_by(is_dead=False)
    elif status_filter_api == 'verified':
        query_api = query_api.filter_by(is_verified=True, is_dead=False)
    elif status_filter_api == 'unverified': # Added unverified filter
        query_api = query_api.filter_by(is_verified=False, is_dead=False)
    elif status_filter_api == 'with_token':
        query_api = query_api.filter(Account.access_token.isnot(None), Account.is_dead==False)
    # 'all' means no status filter beyond is_dead=False if not specified otherwise by other types
    # If 'all' is passed, and no other conditions, it will take all accounts.
    # Consider if 'all' should imply 'is_dead=False' by default for most API use cases.
    # For now, 'all' means truly all, including dead ones unless another filter applies.
    # The web UI for 'accounts' page implies 'all' means all, and then specific filters.
    # Let's keep it simple: if status is 'all', no specific status filter is added here.

    # Get total count before pagination (respecting filters)
    total_count_api = query_api.count() # Renamed
    
    # Get paginated results
    accounts_list_api = query_api.order_by(Account.id.desc()).offset(offset_api).limit(limit_api).all() # Renamed
    
    # Convert to dictionaries
    account_dicts_api = [acc.to_dict() for acc in accounts_list_api] # Renamed
    
    return jsonify({
        "status": "success",
        "total": total_count_api,
        "limit": limit_api,
        "offset": offset_api,
        "accounts": account_dicts_api
    })


@app.route('/api/account/<int:account_id>/verify', methods=['POST'])
@login_required
@admin_required
def api_verify_account(account_id):
    """
    API endpoint to mark an account as verified.
    
    Returns:
    - JSON with success message
    - JSON with error message on failure
    """
    account_to_verify = Account.query.get_or_404(account_id) # Renamed
    
    try:
        account_to_verify.is_verified = True
        account_to_verify.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            "status": "success",
            "message": f"Account {account_to_verify.email} marked as verified."
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

@app.route('/api/account/<int:account_id>/unverify', methods=['POST'])
@login_required
@admin_required
def api_unverify_account(account_id):
    """
    API endpoint to mark an account as not verified.
    
    Returns:
    - JSON with success message
    - JSON with error message on failure
    """
    account_to_unverify = Account.query.get_or_404(account_id) # Renamed
    
    try:
        account_to_unverify.is_verified = False
        account_to_unverify.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            "status": "success",
            "message": f"Account {account_to_unverify.email} marked as not verified."
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

@app.route('/api/account/<int:account_id>/status', methods=['POST'])
@login_required
@admin_required
def api_update_account_status(account_id):
    """
    API endpoint to update an account's status (dead/active).
    
    Required JSON parameters:
    - is_dead: Boolean indicating whether the account is dead
    
    Returns:
    - JSON with success message
    - JSON with error message on failure
    """
    account_status_update = Account.query.get_or_404(account_id) # Renamed
    
    # Get request data
    data = request.json
    if not data:
        return jsonify({"status": "error", "message": "No data provided"}), 400
    
    is_dead_status = data.get('is_dead') # Renamed
    if is_dead_status is None: # Check for None explicitly
        return jsonify({"status": "error", "message": "is_dead parameter is required"}), 400
    
    try:
        account_status_update.is_dead = bool(is_dead_status)
        account_status_update.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            "status": "success",
            "message": f"Account {account_status_update.email} marked as {'dead' if is_dead_status else 'active'}."
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

@app.route('/delete-all-accounts', methods=['GET', 'POST'])
@login_required
@admin_required
def delete_all_accounts():
    """Page to delete all accounts."""
    if request.method == 'POST':
        # Verify confirmation input
        confirmation = request.form.get('confirmation')
        if confirmation != 'DELETE ALL ACCOUNTS':
            flash('Invalid confirmation text. Please type "DELETE ALL ACCOUNTS" to confirm.', 'danger')
            return redirect(url_for('delete_all_accounts'))
        
        try:
            # Get count before deletion
            count_deleted = Account.query.count() # Renamed
            
            # Delete all accounts
            Account.query.delete()
            db.session.commit()
            
            flash(f'Successfully deleted all {count_deleted} accounts.', 'success')
            return redirect(url_for('dashboard'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error deleting accounts: {str(e)}', 'danger')
            return redirect(url_for('delete_all_accounts'))
    
    return render_template('delete_all_accounts.html')

@app.route('/api/accounts/delete-all', methods=['DELETE'])
@login_required
@admin_required
def api_delete_all_accounts():
    """
    API endpoint to delete all accounts.
    
    Returns:
    - JSON with success message and count of deleted accounts
    - JSON with error message on failure
    """
    try:
        count_api_deleted = Account.query.count() # Renamed
        Account.query.delete()
        db.session.commit()
        
        return jsonify({
            "status": "success",
            "message": f"Successfully deleted all {count_api_deleted} accounts.",
            "count": count_api_deleted
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "status": "error",
            "message": f"Error deleting accounts: {str(e)}"
        }), 500

@app.route('/api/upload-accounts', methods=['POST'])
# @login_required # API key auth would be better here
# @admin_required
def api_upload_accounts():
    """
    API endpoint to upload accounts.

    Required POST parameters:
    - accounts: List of account objects with email and password fields

    Returns:
    - JSON with results
    """
    # Basic API Key Auth (Example - replace with a robust mechanism)
    api_key = request.headers.get('X-API-KEY')
    expected_api_key = os.environ.get('ADMIN_API_KEY', 'SUPER_SECRET_API_KEY_CHANGE_ME') 
    if not api_key or api_key != expected_api_key:
         return jsonify({"error": "Unauthorized: Invalid or missing API Key"}), 401


    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    accounts_data_upload = data.get('accounts') # Renamed
    if not accounts_data_upload or not isinstance(accounts_data_upload, list):
        return jsonify({"error": "Invalid accounts data: 'accounts' must be a list"}), 400

    added_api = 0 # Renamed
    updated_api = 0 # Renamed
    errors_api = [] # Renamed

    for account_item in accounts_data_upload: # Renamed
        email_item = account_item.get('email') # Renamed
        password_item = account_item.get('password') # Renamed

        if not email_item or not password_item:
            errors_api.append({
                "account": account_item,
                "error": "Missing email or password"
            })
            continue

        # Check if account already exists
        existing_account_api = Account.query.filter_by(email=email_item).first() # Renamed

        if existing_account_api:
            # Update existing account
            existing_account_api.password = password_item
            existing_account_api.updated_at = datetime.utcnow()
            updated_api += 1
        else:
            # Create new account
            new_account_api = Account( # Renamed
                email=email_item,
                password=password_item
            )
            db.session.add(new_account_api)
            added_api += 1

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    return jsonify({
        "status": "success",
        "added": added_api,
        "updated": updated_api,
        "errors": errors_api
    })

@app.route('/verification-emails', methods=['GET'])
@login_required
@admin_required
def verification_emails():
    """Verification emails management page."""
    # Get all verification emails from database
    emails_list = VerificationEmail.query.order_by(VerificationEmail.id.desc()).all() # Renamed
    
    # Calculate statistics
    total_emails_stats = len(emails_list) # Renamed
    active_emails_stats = sum(1 for e in emails_list if e.is_active and not e.is_burned) # Renamed
    burned_emails_stats = sum(1 for e in emails_list if e.is_burned) # Renamed
    
    # Calculate average success rate
    total_verifications_stats = sum(e.verification_count for e in emails_list) # Renamed
    total_successes_stats = sum(e.success_count for e in emails_list) # Renamed
    avg_success_rate_stats = 0 # Renamed
    if total_verifications_stats > 0:
        avg_success_rate_stats = round((total_successes_stats / total_verifications_stats) * 100, 2)
    
    stats_summary = { # Renamed
        'total_emails': total_emails_stats,
        'active_emails': active_emails_stats,
        'burned_emails': burned_emails_stats,
        'avg_success_rate': avg_success_rate_stats
    }
    
    return render_template('verification_emails.html', emails=emails_list, stats=stats_summary)


@app.route('/api/verification-emails/<int:email_id>', methods=['GET'])
@login_required
@admin_required
def get_verification_email(email_id):
    """API endpoint to get a verification email by ID."""
    email_obj = VerificationEmail.query.get_or_404(email_id) # Renamed
    
    return jsonify({
        'status': 'success',
        'email': email_obj.to_dict() # Ensure to_dict() is comprehensive
    })

@app.route('/api/verification-emails/<int:email_id>/test', methods=['GET'])
@login_required
@admin_required
def test_verification_email_connection(email_id):
    """API endpoint to test connection to a verification email's IMAP server."""
    email_to_test = VerificationEmail.query.get_or_404(email_id) # Renamed
    
    try:
        # Try to connect to IMAP server
        import imaplib # Keep import local if only used here
        
        print(f"Testing connection to {email_to_test.imap_server}:{email_to_test.imap_port} for {email_to_test.email}")
        # Use a timeout for IMAP connection
        with imaplib.IMAP4_SSL(email_to_test.imap_server, email_to_test.imap_port, timeout=10) as imap_conn: # Renamed
            # Try to login
            print(f"Logging in as {email_to_test.email}")
            imap_conn.login(email_to_test.email, email_to_test.password)
            
            # Try to select inbox
            print(f"Selecting inbox")
            imap_conn.select('INBOX')
            
            # Logout
            imap_conn.logout()
        
        return jsonify({
            'status': 'success',
            'message': 'Connection test successful'
        })
    except imaplib.IMAP4.error as imap_err: # Catch specific IMAP errors
        print(f"IMAP connection test failed: {str(imap_err)}")
        return jsonify({
            'status': 'error',
            'message': f'IMAP Connection test failed: {str(imap_err)}'
        })
    except Exception as e:
        print(f"Connection test failed with general error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Connection test failed: {str(e)}'
        })


@app.route('/api/verification-emails/<int:email_id>/reset-stats', methods=['POST'])
@login_required
@admin_required
def reset_verification_email_stats(email_id):
    """API endpoint to reset statistics for a verification email."""
    email_stats_reset = VerificationEmail.query.get_or_404(email_id) # Renamed
    
    try:
        email_stats_reset.verification_count = 0
        email_stats_reset.success_count = 0
        email_stats_reset.failure_count = 0
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Statistics reset successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'Failed to reset statistics: {str(e)}'
        })

@app.route('/api/verification-emails/toggle-active', methods=['POST'])
@login_required
@admin_required
def toggle_verification_email_active():
    """API endpoint to toggle a verification email's active status."""
    data = request.json
    if not data:
        return jsonify({'status': 'error', 'message': 'No data provided'}), 400
    
    email_id_toggle = data.get('email_id') # Renamed
    is_active_toggle = data.get('is_active') # Renamed
    
    if email_id_toggle is None or is_active_toggle is None: # Explicit None check
        return jsonify({'status': 'error', 'message': 'Missing required fields: email_id and is_active'}), 400
    
    email_obj_toggle = VerificationEmail.query.get_or_404(email_id_toggle) # Renamed
    
    try:
        email_obj_toggle.is_active = bool(is_active_toggle)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'Email {email_obj_toggle.email} {"activated" if is_active_toggle else "deactivated"} successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'Failed to update status: {str(e)}'
        })

@app.route('/api/verification-emails/toggle-burned', methods=['POST'])
@login_required
@admin_required
def toggle_verification_email_burned():
    """API endpoint to toggle a verification email's burned status."""
    data = request.json
    if not data:
        return jsonify({'status': 'error', 'message': 'No data provided'}), 400
    
    email_id_burned = data.get('email_id') # Renamed
    is_burned_status = data.get('is_burned') # Renamed
    
    if email_id_burned is None or is_burned_status is None: # Explicit None check
        return jsonify({'status': 'error', 'message': 'Missing required fields: email_id and is_burned'}), 400
    
    email_obj_burned = VerificationEmail.query.get_or_404(email_id_burned) # Renamed
    
    try:
        email_obj_burned.is_burned = bool(is_burned_status)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'Email {email_obj_burned.email} marked as {"burned" if is_burned_status else "not burned"} successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'Failed to update status: {str(e)}'
        })

@app.route('/add-verification-email', methods=['POST'])
@login_required
@admin_required
def add_verification_email():
    """Add a new verification email."""
    email_addr = request.form.get('email') # Renamed
    password_val = request.form.get('password') # Renamed
    refresh_token_val = request.form.get('refresh_token') # Renamed
    client_id_val = request.form.get('client_id') # Renamed
    imap_server_val = request.form.get('imap_server') # Renamed
    imap_port_val = request.form.get('imap_port', 993, type=int) # Renamed
    smtp_server_val = request.form.get('smtp_server') # Renamed
    smtp_port_val = request.form.get('smtp_port', 587, type=int) # Renamed
    is_active_val = request.form.get('is_active', 'off') == 'on' # Renamed
    
    if not email_addr or not imap_server_val: # Password can be optional if refresh token is used
        flash('Email address and IMAP server are required', 'danger')
        return redirect(url_for('verification_emails'))
    if not password_val and not (refresh_token_val and client_id_val):
        flash('Either a password or both a refresh token and client ID are required.', 'danger')
        return redirect(url_for('verification_emails'))

    # Check if email already exists
    existing_email_add = VerificationEmail.query.filter_by(email=email_addr).first() # Renamed
    if existing_email_add:
        flash(f'Email {email_addr} already exists', 'danger')
        return redirect(url_for('verification_emails'))
    
    try:
        # Create new verification email
        new_email_add = VerificationEmail( # Renamed
            email=email_addr,
            password=password_val, # Store even if empty, model might not allow null
            refresh_token=refresh_token_val,
            client_id=client_id_val,
            imap_server=imap_server_val,
            imap_port=imap_port_val,
            smtp_server=smtp_server_val,
            smtp_port=smtp_port_val,
            is_active=is_active_val
        )
        
        db.session.add(new_email_add)
        db.session.commit()
        
        flash(f'Verification email {email_addr} added successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error adding verification email: {str(e)}', 'danger')
    
    return redirect(url_for('verification_emails'))


@app.route('/delete-verification-email', methods=['POST'])
@login_required
@admin_required
def delete_verification_email():
    """Delete a verification email."""
    email_id_delete = request.form.get('email_id', type=int) # Renamed
    
    if not email_id_delete:
        flash('Email ID is required', 'danger')
        return redirect(url_for('verification_emails'))
    
    email_obj_delete = VerificationEmail.query.get_or_404(email_id_delete) # Renamed
    
    # Don't allow deleting emails that are in use
    if email_obj_delete.in_use:
        flash(f'Cannot delete verification email {email_obj_delete.email} because it is currently in use', 'danger')
        return redirect(url_for('verification_emails'))
    
    try:
        db.session.delete(email_obj_delete)
        db.session.commit()
        
        flash(f'Verification email {email_obj_delete.email} deleted successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting verification email: {str(e)}', 'danger')
    
    return redirect(url_for('verification_emails'))

# This route seems unused by the frontend, but kept for completeness or future use
@app.route('/test-verification-email', methods=['GET'])
@login_required
@admin_required
def test_verification_email():
    """Test a verification email setup."""
    # This will be implemented in the frontend using AJAX if needed
    # For now, it just renders a template.
    return render_template('test_verification_email.html') # Assuming this template exists or will be created

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found", "message": str(error)}), 404

@app.errorhandler(403)
def forbidden(error):
    return jsonify({"error": "Forbidden", "message": str(error)}), 403

@app.errorhandler(500)
def server_error(error):
    # Log the actual error for debugging
    app.logger.error(f"Server Error: {error}", exc_info=True)
    return jsonify({"error": "Server error", "message": "An internal server error occurred."}), 500


# Create templates folder if it doesn't exist (usually not needed if using Flask structure)
templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
os.makedirs(templates_dir, exist_ok=True)

if __name__ == '__main__':
    # Setup logging for development
    if app.config['DEBUG']:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
        # Add more robust logging for production (e.g., file handler, rotating logs)

    app.run(host='0.0.0.0', port=5000)
